import React, { useState, useMemo } from "react";
import GenericTable from "../../Components/GenericTable";
import { FilterButtons } from "../../Components/GenericTable";
import { useTranslation } from "react-i18next";
import newWindow from "../../Images/new-window.svg";
// Example data for the table
const initialTeams = [
  {
    id: 1,
    name: "<PERSON><PERSON> Khiladi<PERSON>",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "Service Contractor",
    jobTitle: "",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "Service Contractor",
    jobTitle: "",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 3,
    name: "<PERSON>",
    uid: "*********",
    type: "Contractor",
    company: "Oracle",
    organization: "-",
    jobTitle: "-",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 4,
    name: "Piney Spiro",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "-",
    jobTitle: "-",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
  {
    id: 5,
    name: "Picklyman Proto",
    uid: "*********",
    type: "Non Billable",
    company: "Oracle",
    organization: "-",
    jobTitle: "-",
    manager: "ANSHAN SANDRA",
    expirationDate: "Dec-31-2025",
    status: "Approved",
  },
];

const MyStaff = () => {
  const { t } = useTranslation();
  const [teams] = useState(initialTeams);
  const [activeTab, setActiveTab] = useState("directReports");
  const [hoveredRow, setHoveredRow] = useState(null); // Moved state here

  const filterOptions = [
    { value: "directReports", label: t("my_staff.filter.direct_reports") },
    { value: "myOrg", label: t("my_staff.filter.my_org") },
  ];

  // Table columns
  const columns = [
    {
      name: t("my_staff.name"),
      selector: (row) => row.name,
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)} // Use setHoveredRow here
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => window.open(`/team-details`, "_blank")}
        >
          <img src={row.image} alt={row.name} className="w-8 h-8 rounded-full" />
          <span>{row.name}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt={t("my_staff.open")} />
          )}
        </div>
      ),
    },
    {
      name: t("my_staff.uid"),
      selector: (row) => row.uid,
    },
    {
      name: t("my_staff.type"),
      selector: (row) => row.type,
    },
    {
      name: t("my_staff.company"),
      selector: (row) => row.company,
    },
    {
      name: t("my_staff.organization"),
      selector: (row) => row.organization,
    },
    {
      name: t("my_staff.job_title"),
      selector: (row) => row.jobTitle,
    },
    {
      name: t("my_staff.manager"),
      selector: (row) => row.manager,
    },
    {
      name: t("my_staff.expiration_date"),
      selector: (row) => row.expirationDate,
    },
    {
      name: t("my_staff.status"),
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${
            row.status.toLowerCase() === "approved"
              ? "bg-[#107C0F1A] bg-opacity-10 text-[#00BA00]"
              : "bg-[#E21B1B14] bg-opacity-8 text-[#D15858]"
          }`}
        >
          {t(`my_staff.status_${row.status.toLowerCase()}`)}
        </span>
      ),
    },
  ];

  const filteredTeams = useMemo(() => {
    if (activeTab === "directReports") {
      return teams.slice(0, 3);
    } else {
      return teams;
    }
  }, [teams, activeTab]);

  const handleAddTeam = () => {
    alert("Add Team Member Clicked!");
  };

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      {/* Header */}
      <div className="text-[24px] font-normal text-[#4F2683] mb-4">
        <h3>{t("my_staff.title")}</h3>
      </div>

      {/* Filter Buttons */}
      <FilterButtons
        filter={activeTab}
        onFilterChange={setActiveTab}
        filterOptions={filterOptions}
      />

      {/* Table Section */}
      <div className="mt-4">
        <GenericTable
          title={t("my_staff.title")}
          showAddButton={false}
          columns={columns}
          data={filteredTeams}
          onAdd={handleAddTeam}
        />
      </div>
    </div>
  );
};

export default MyStaff;
