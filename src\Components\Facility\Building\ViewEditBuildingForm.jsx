import React, { useState, useEffect, useMemo } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import Loader from "../../Loader.jsx";
import { updateBuilding } from "../../../api/facility";
import { toast } from "react-toastify";
import { useBuildingMasterData } from "../../../hooks/useBuildingMasterData";

// Validation schema (note: numeric fields are validated with yup.number())
const buildingSchema = yup.object().shape({
  facility_id: yup.string().required("Facility is required"),
  name: yup.string().required("Building Name is required"),
  building_code: yup.string().required("Building Code is required"),
  status: yup.number().required("Status is required"),
  type: yup.number().required("Building Type is required"),
  occupancy_type: yup.number().required("Occupancy Type is required"),
  phone: yup.string().required("Building Phone is required"),
  email: yup
    .string()
    .email("Enter a valid email")
    .required("Building Email is required"),
  geo_location_code: yup.number().nullable(),
  other_code: yup.string().nullable(),
  building_url: yup.string().url("Enter a valid URL").nullable(),
  connected_applications: yup.string().nullable(),
  notes: yup.string().nullable(),
  address: yup.string().required("Address is required"),
  city: yup.string().nullable("City is required"),
});

const ViewEditBuildingForm = ({ buildingData, onClose, facility, fetchBuildings }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const { statusOptions, typeOptions, occupancyOptions } = useBuildingMasterData();


  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(buildingSchema),
    defaultValues: {
      facility_id: buildingData.facility_id || "",
      name: buildingData.name || "",
      building_code: buildingData.building_code || "",
      status: buildingData.status,
      type: buildingData.type,
      occupancy_type: buildingData.occupancy_type,
      phone: buildingData.phone || "",
      email: buildingData.email || "",
      geo_location_code: buildingData.geo_location_code ? Number(buildingData.geo_location_code) : null,
      other_code: buildingData.other_code || "",
      building_url: buildingData.building_url || "",
      connected_applications: buildingData.connected_applications || "",
      notes: buildingData.notes || "",
      address: buildingData.address || "",
      city: buildingData.city || "",
    },
  });

  // Reset the form when buildingData changes
  useEffect(() => {
    reset({
      facility_id: buildingData.facility_id || "",
      name: buildingData.name || "",
      building_code: buildingData.building_code || "",
      status: buildingData.status,
      type: buildingData.type,
      occupancy_type: buildingData.occupancy_type,
      phone: buildingData.phone || "",
      email: buildingData.email || "",
      geo_location_code: buildingData.geo_location_code ? Number(buildingData.geo_location_code) : null,
      other_code: buildingData.other_code || "",
      building_url: buildingData.building_url || "",
      connected_applications: buildingData.connected_applications || "",
      notes: buildingData.notes || "",
      address: buildingData.address || "",
      city: buildingData.city || "",
    });
  }, [buildingData, reset]);

  // Facility dropdown options (static for now)
  const facilityOptions = useMemo(
    () => [
      { key: facility.facility_id, label: buildingData.facility?.name || "", value: facility.name },
    ],
    [facility, buildingData.facility]
  );

  // Mapped field configuration for main building details
  const buildingFields = [
    {
      label: "Facility",
      name: "facility_id",
      type: "dropdown",
      options: facilityOptions,
      viewValue: buildingData.facility?.name || buildingData.facility_id,
    },
    {
      label: "Building Name",
      name: "name",
      type: "text",
    },
    {
      label: "Building Code",
      name: "building_code",
      type: "text",
    },
    {
      label: "Status",
      name: "status",
      type: "dropdown",
      options: statusOptions,
      viewValue: buildingData.building_status_name?.value || buildingData.status,
    },
    {
      label: "Type",
      name: "type",
      type: "dropdown",
      options: typeOptions,
      viewValue: buildingData.building_type_name?.value || buildingData.type,
    },
    {
      label: "Occupancy Type",
      name: "occupancy_type",
      type: "dropdown",
      options: occupancyOptions,
      viewValue: buildingData.building_occupancy_type_name?.value || buildingData.occupancy_type,
    },
    {
      label: "Building Phone",
      name: "phone",
      type: "text",
    },
    {
      label: "Building Email",
      name: "email",
      type: "email",
    },
    {
      label: "Geo Location Code",
      name: "geo_location_code",
      type: "text",
    },
    {
      label: "Other Code",
      name: "other_code",
      type: "text",
    },
    {
      label: "Building URL",
      name: "building_url",
      type: "text",
    },
    {
      label: "Connected Application",
      name: "connected_applications",
      type: "text",
    },
    {
      label: "Building Notes",
      name: "notes",
      type: "textarea",
    },
  ];

  // Mapped field configuration for the address section
  const addressFields = [
    {
      label: "Address",
      name: "address",
      type: "text",
    },
    {
      label: "City",
      name: "city",
      type: "text",
    },
  ];

  // Handle form submission
  const onSubmit = async (data) => {
    try {
      const { facility_id, city, connected_applications, ...payload } = data;
      const buildingId = buildingData.building_id;
      await updateBuilding(buildingData.facility_id, buildingId, payload);
      toast.success("Building updated successfully!");
      setIsEditMode(false);
      onClose();
      fetchBuildings();
    } catch (error) {
      toast.error(error.response && error.response.data
        ? error.response.data.message
        : "Failed to update building.");
    }
  };

  const handleCancelEdit = () => {
    reset({
      facility_id: buildingData.facility_id || "",
      name: buildingData.name || "",
      building_code: buildingData.building_code || "",
      status: buildingData.status,
      type: buildingData.type,
      occupancy_type: buildingData.occupancy_type,
      phone: buildingData.phone || "",
      email: buildingData.email || "",
      geo_location_code: buildingData.geo_location_code ? Number(buildingData.geo_location_code) : null,
      other_code: buildingData.other_code || "",
      building_url: buildingData.building_url || "",
      connected_applications: buildingData.connected_applications || "",
      notes: buildingData.notes || "",
      address: buildingData.address || "",
      city: buildingData.city || "",
    });
    setIsEditMode(false);
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
  <div className="bg-white w-full h-full max-w-5xl p-4 rounded-lg shadow-lg overflow-y-auto">
    <div className="flex justify-between items-center">
      <h2 className="text-[30px] text-[#4F2683] font-normal">Building Details</h2>
      <button
        onClick={onClose}
        className="flex items-center justify-center bg-[#4F2683] text-white text-2xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
      >
        &times;
      </button>
    </div>
    <hr className="mb-4 mt-2" />

    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Building Fields */}
      {buildingFields.map((field, idx) => (
        <div key={idx} className="flex mb-4 items-center">
          <label className="mr-2 w-1/3 text-[16px] font-normal">{field.label}</label>
          <div className="w-2/3">
            {isEditMode ? (
              field.type === "dropdown" ? (
                field.loader ? (
                  <Loader />
                ) : (
                  <Controller
                    control={control}
                    name={field.name}
                    render={({ field: controllerField }) => (
                      <CustomDropdown
                        className="h-11 rounded border-gray-300"
                        placeholder={field.placeholder || `Select ${field.label}`}
                        options={field.options}
                        onSelect={controllerField.onChange}
                        selectedOption={controllerField.value}
                        value={controllerField.value}
                        hoverBgColor="hover:bg-[#4F2683]"
                        borderColor="border-gray-300"
                      />
                    )}
                  />
                )
              ) : (
                <Input
                  type={field.type}
                  {...register(field.name)}
                  disabled={!isEditMode}
                  className="w-full border rounded p-2"
                />
              )
            ) : (
              <Input
                type={field.type === "dropdown" ? "text" : field.type}
                value={field.type === "dropdown" ? field.viewValue : buildingData[field.name] || ""}
                disabled
                className="w-full border-none text-[#8F8F8F]"
              />
            )}
            {errors[field.name] && (
              <p className="text-red-500 text-sm">{errors[field.name].message}</p>
            )}
          </div>
        </div>
      ))}

      {/* Address Fields */}
      <h2 className="text-[20px] pt-2 text-[#333333] font-medium pb-4">Address</h2>
      {addressFields.map((field, idx) => (
        <div key={idx} className="flex mb-4 items-center">
          <label className="mr-2 w-1/3 text-[16px] font-normal">{field.label}</label>
          <div className="w-2/3">
            {isEditMode ? (
              <Input
                type={field.type}
                {...register(field.name)}
                disabled={!isEditMode}
                className="w-full border rounded p-2"
              />
            ) : (
              <Input
                type={field.type}
                value={buildingData[field.name] || ""}
                disabled
                className="w-full border-none text-[#8F8F8F]"
              />
            )}
            {errors[field.name] && (
              <p className="text-red-500 text-sm">{errors[field.name].message}</p>
            )}
          </div>
        </div>
      ))}

      {/* Edit/Save/Cancel Actions */}
      <div className="flex gap-4 justify-end mt-6">
        {!isEditMode ? (
          <button
            type="button"
            onClick={() => setIsEditMode(true)}
            className="px-4 py-2 bg-[#4F2683] text-white rounded"
          >
            Edit
          </button>
        ) : (
          <>
            <button
              type="button"
              onClick={handleCancelEdit}
              className="px-4 py-2 bg-[#979797] text-white rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Save
            </button>
          </>
        )}
      </div>
    </form>
  </div>
</div>

  );
};

export default ViewEditBuildingForm;
