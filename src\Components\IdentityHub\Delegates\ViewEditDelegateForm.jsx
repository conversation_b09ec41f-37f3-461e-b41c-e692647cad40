import React, { useState, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Input from "../../Global/Input/Input";
import Button from "../../Global/Button";

const dummyStatusOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 2 },
];

const delegateSchema = yup.object().shape({
  name: yup.string().required("Name is required"),
  uid: yup.string().required("UID is required"),
  start_date: yup.date().required("Start Date is required"),
  end_date: yup.date().required("End Date is required"),
  status: yup.number().required("Status is required"),
});

const ViewEditDelegateForm = ({ delegateData, onClose, onUpdateDelegate }) => {
  const [isEditMode, setIsEditMode] = useState(false);

  const getDefaultValues = (data) => ({
    name: data.name || "",
    uid: data.uid || "",
    start_date: data.start_date ? data.start_date: "",
    end_date: data.end_date ? data.end_date: "",
    status: data.status === "Active" ? 1 : 2,
  });

  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(delegateSchema),
    defaultValues: getDefaultValues(delegateData),
  });

  useEffect(() => {
    reset(getDefaultValues(delegateData));
  }, [delegateData, reset]);

  const onSubmit = (data) => {
    const updatedDelegate = {
      ...delegateData,
      ...data,
      start_date: new Date(data.start_date),
      end_date: new Date(data.end_date),
      status: data.status === 1 ? "Active" : "Inactive",
    };
    onUpdateDelegate(updatedDelegate);
    setIsEditMode(false);
  };

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode
      ? "focus:outline-none border-gray-300"
      : "border-none text-[#8F8F8F]"
  }`;

  return (
    <div className="w-full p-0">
      {/* Header */}
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          View/Edit Delegate
        </h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-6 rounded-lg">
        <h2 className="text-[20px] text-[#333333] font-medium pb-4">
          Delegate Details
        </h2>

        {/* Name */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="name"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Name
          </label>
          <div className="w-3/4">
            <Input
              id="name"
              placeholder="Name"
              {...register("name")}
              disabled={!isEditMode}
              className={inputClassName}
            />
            {errors.name && (
              <p className="text-red-500 text-sm">{errors.name.message}</p>
            )}
          </div>
        </div>

        {/* UID */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="uid"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            UID
          </label>
          <div className="w-3/4">
            <Input
              id="uid"
              placeholder="UID"
              {...register("uid")}
              disabled={!isEditMode}
              className={inputClassName}
            />
            {errors.uid && (
              <p className="text-red-500 text-sm">{errors.uid.message}</p>
            )}
          </div>
        </div>

        {/* Start Date */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="start_date"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Start Date
          </label>
          <div className="w-3/4">
            <Input
              type="date"
              id="start_date"
              {...register("start_date")}
              disabled={!isEditMode}
              className={inputClassName}
            />
            {errors.start_date && (
              <p className="text-red-500 text-sm">
                {errors.start_date.message}
              </p>
            )}
          </div>
        </div>

        {/* End Date */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="end_date"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            End Date
          </label>
          <div className="w-3/4">
            <Input
              type="date"
              id="end_date"
              {...register("end_date")}
              disabled={!isEditMode}
              className={inputClassName}
            />
            {errors.end_date && (
              <p className="text-red-500 text-sm">
                {errors.end_date.message}
              </p>
            )}
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="status"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Status
          </label>
          <div className="w-3/4">
            {isEditMode ? (
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <select
                    {...field}
                    id="status"
                    className="w-full border border-gray-300 rounded p-2 focus:outline-none"
                  >
                    {dummyStatusOptions.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                )}
              />
            ) : (
              <Input
                type="text"
                id="status"
                value={delegateData.status}
                disabled
                className={inputClassName}
              />
            )}
            {errors.status && (
              <p className="text-red-500 text-sm">{errors.status.message}</p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-end mt-8">
          {isEditMode ? (
            <>
              <Button
                type="button"
                onClick={() => {
                  setIsEditMode(false);
                  reset(getDefaultValues(delegateData));
                }}
                label="Cancel"
                className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition-colors"
              />
              <Button
                type="submit"
                label="Save Changes"
                className="px-6 py-2 bg-[#4F2683] text-white rounded hover:bg-[#3c1d64] transition-colors"
              />
            </>
          ) : (
            // Using native button for Edit action to ensure onClick works properly
            <button
              type="button"
              onClick={() => setIsEditMode(true)}
              className="px-6 py-2 bg-[#4F2683] text-white rounded hover:bg-[#3c1d64] transition-colors"
            >
              Edit
            </button>
          )}
          <Button
            type="button"
            onClick={onClose}
            label="Close"
            className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 transition-colors"
          />
        </div>
      </form>
    </div>
  );
};

export default ViewEditDelegateForm;
