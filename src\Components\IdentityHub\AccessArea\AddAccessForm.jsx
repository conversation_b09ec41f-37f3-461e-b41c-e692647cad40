import React, { useEffect, useState } from "react";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import SearchBar from "../../Global/SearchBar";
import { useLocation } from "react-router-dom";
import {
  createIdentityAccess,
  getAccessLevel,
  getCardNumber,
} from "../../../api/identity";
import { useCardData } from "../../../hooks/useCardData"; // <-- Import the hook for master data
import { toast } from "react-toastify";

const AddAccessForm = ({ onSubmit, onClose }) => {
  const { statusOptions } = useCardData(); // <-- Use master data status options
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");
  const [areaName, setAreaName] = useState("");
  const [cardNumber, setCardNumber] = useState("");
  const [cardOptions, setCardOptions] = useState([]);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [status, setStatus] = useState("");
  const [accessOptions, setAccessOptions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [accessAreaId, setAccessAreaId] = useState(null);
  const [cardId, setCardId] = useState(null);

  const handleAccessSelect = (selected) => {
    setAreaName(selected.name);
    setAccessAreaId(selected.id);
    setShowSuggestions(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields and show specific field names in toast
    const requiredFields = [];

    if (!accessAreaId) {
      requiredFields.push("Access Area");
    }
    if (!cardId) {
      requiredFields.push("Card Number");
    }
    if (!startDate) {
      requiredFields.push("Start Date");
    }
    if (!status) {
      requiredFields.push("Status");
    }

    if (requiredFields.length > 0) {
      const fieldNames = requiredFields.join(", ");
      toast.error(`Please fill the following required field(s): ${fieldNames}`);
      return;
    }

    const payload = {
      access_level_id: accessAreaId,
      card_id: cardId,
      start_date: startDate,
      end_date: endDate ,
      status: status,
      identity_id: identityId,
    };

    try {
       const response = await createIdentityAccess(payload);
    onSubmit(response.data)

      setAreaName("");
      setAccessAreaId(null);
      setCardNumber("");
      setCardId(null);
      setStartDate("");
      setEndDate("");
      setStatus("");
      toast.success("Access added successfully!");
    } catch (error) {
      console.error("Error adding access:", error);
      toast.error("Failed to add access. Please try again.");
    }
  };

  const handleAccessSearch = async (input) => {
    setAreaName(input);
    setShowSuggestions(true);

    try {
      const result = await getAccessLevel({ search: input });
      setAccessOptions(result);
    } catch (err) {
      setAccessOptions([]);
    }
  };

  useEffect(() => {
    const fetchCardNumbers = async () => {
      try {
        const response = await getCardNumber();
        // Defensive: filter only objects with both id and card_number (avoid undefined/null/empty)
        const validCards = Array.isArray(response?.data)
          ? response.data.filter(
              (card) =>
                card &&
                typeof card === "object" &&
                card.id !== undefined &&
                card.id !== null &&
                card.card_number !== undefined &&
                card.card_number !== null
            )
          : [];
        setCardOptions(validCards);
      } catch (error) {
        setCardOptions([]);
      }
    };

    fetchCardNumbers();
  }, []);

  return (
    <div className="w-full p-0">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Add Access Area
        </h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg">
        <h2 className="text-[20px] text-[#333333] font-medium pb-4">
          Access Details
        </h2>

        {/* Access Area */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="areaName"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Access Area
          </label>
          <div className="w-3/4">
            <SearchBar
              placeholder="Search Access Area"
              value={areaName}
              onInputChange={handleAccessSearch}
              onClick={() => setShowSuggestions(true)}
            />
            {showSuggestions && accessOptions.length > 0 && (
              <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded mt-1 max-h-48 overflow-y-auto shadow-md">
                {accessOptions
                  .filter((area) => area && area.id && area.name)
                  .map((area) => (
                    <li
                      key={area.id}
                      className="px-3 py-2 hover:bg-[#4F2683] hover:text-white cursor-pointer"
                      onClick={() => handleAccessSelect(area)}
                    >
                      {area.name}
                    </li>
                  ))}
              </ul>
            )}
          </div>
        </div>

        {/* Card Number */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="cardNumber"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Card Number
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={
                Array.isArray(cardOptions)
                  ? cardOptions
                      .filter(
                        (card) =>
                          card &&
                          typeof card === "object" &&
                          card.id !== undefined &&
                          card.id !== null &&
                          card.card_number !== undefined &&
                          card.card_number !== null
                      )
                      .map((card) => ({
                        label: card.card_number,
                        value: card.id,
                      }))
                  : []
              }
              placeholder="Card Number"
              onSelect={(selectedId) => {
                setCardId(selectedId);
                const matched =
                  Array.isArray(cardOptions) && selectedId
                    ? cardOptions.find(
                        (c) =>
                          c &&
                          c.id !== undefined &&
                          c.id !== null &&
                          c.id === selectedId
                      )
                    : undefined;
                if (matched) setCardNumber(matched.card_number);
              }}
              selectedOption={cardNumber}
              value={cardId}
              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        {/* Start Date */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="startDate"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Start Date
          </label>
          <div className="w-3/4">
            <DateInput
              name="startDate"
              className=" w-full"
              id="startDate"
              value={startDate}
              onChange={(date) => {
                if (date) {
                  // Convert the date to the local format
                  const localDate = new Date(date);
                  setStartDate(localDate.toLocaleDateString("en-US")); // Format: MM/DD/YYYY
                } else {
                  setStartDate("");
                }
              }}
              placeholder="MM-DD-YYYY"
            />
          </div>
        </div>

        {/* End Date */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="endDate"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            End Date
          </label>
          <div className="w-3/4">
            <DateInput
              name="endDate"
              className=" w-full"
              id="endDate"
              value={endDate}
              onChange={(date) => {
                if (date) {
                  const localDate = new Date(date);
                  setEndDate(localDate.toLocaleDateString("en-US")); // Format: MM/DD/YYYY
                } else {
                  setEndDate("");
                }
              }}
              placeholder="MM-DD-YYYY"
            />
          </div>
        </div>

        {/* Status */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="status"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Status
          </label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={statusOptions?.filter((opt) => opt?.value) || []}
              placeholder="Select Status"
              onSelect={(option) => setStatus(option)}
              selectedOption={status}
              value={status}
              hoverBgColor="hover:bg-[#4F2683]"
              borderColor="border-gray-300"
            />
          </div>
        </div>

        <div className="flex gap-4 pb-4 justify-center">
          <Button
            type="cancel"
            label="Cancel"
            onClick={onClose}
            className="bg-gray-400 text-white"
          />
          <Button type="primary" label="Add" className="text-white" />
        </div>
      </form>
    </div>
  );
};

export default AddAccessForm;
