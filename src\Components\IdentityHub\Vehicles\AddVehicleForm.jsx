import React, { useState } from "react";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
// import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { createVehicle } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";

const AddVehicleForm = ({ onSubmit, onClose }) => {
  const [plateNumber, setPlateNumber] = useState("");
  const [issuedBy, setIssuedBy] = useState("");
  const [VIN, setVIN] = useState("");
  const [year, setYear] = useState("");
  const [make, setMake] = useState("");
  const [model, setModel] = useState("");
  const [color, setColor] = useState("");
  const [uploadedDate, setUploadedDate] = useState("");
  const [loading, setLoading] = useState(false);

  // Get identity_id from URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields and show specific field names in toast
    const requiredFields = [];

    if (!plateNumber) {
      requiredFields.push("Plate Number");
    }
    if (!issuedBy) {
      requiredFields.push("Issued by");
    }
    if (!VIN) {
      requiredFields.push("VIN");
    }
    if (!year) {
      requiredFields.push("Year");
    }
    if (!make) {
      requiredFields.push("Make");
    }
    if (!model) {
      requiredFields.push("Model");
    }
    if (!uploadedDate) {
      requiredFields.push("Uploaded Date");
    }

    if (requiredFields.length > 0) {
      const fieldNames = requiredFields.join(", ");
      toast.error(`Please fill the following required field(s): ${fieldNames}`);
      return;
    }

    setLoading(true);
    try {
      const vehicleData = {
        plate_number: plateNumber,
        issued_by: issuedBy,
        vin: VIN,
        year: year,
        make: make,
        model: model,
        color: color,
        uploaded_date: uploadedDate,
        identity_id: identityId,
      };

      const result = await createVehicle(vehicleData);
      toast.success("Vehicle added successfully!");
      onSubmit(result);
      onClose();
    } catch (error) {
      console.error("Error adding vehicle:", error);
      toast.error("Failed to add vehicle. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full p-0">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Add Vehicle</h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg">
        <h2 className="text-[20px] text-[#333333] font-medium pb-4">
          Vehicle Details
        </h2>

        {/* Plate Number */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="plateNumber"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Plate Number*
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="plateNumber"
              placeholder="Plate Number"
              value={plateNumber}
              onChange={(e) => setPlateNumber(e.target.value)}
              required
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* Issued by */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="issuedBy"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Issued by*
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="issuedBy"
              placeholder="Issued by"
              value={issuedBy}
              onChange={(e) => setIssuedBy(e.target.value)}
              required
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* VIN */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="VIN"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            VIN*
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="VIN"
              placeholder="VIN"
              value={VIN}
              onChange={(e) => setVIN(e.target.value)}
              required
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* Year */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="year"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Year*
          </label>
          <div className="w-3/4">
            <Input
              type="number"
              id="year"
              placeholder="Year"
              value={year}
              onChange={(e) => setYear(e.target.value)}
              required
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* Make */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="make"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Make*
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="make"
              placeholder="Make"
              value={make}
              onChange={(e) => setMake(e.target.value)}
              required
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* Model */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="model"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Model*
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="model"
              placeholder="Model"
              value={model}
              onChange={(e) => setModel(e.target.value)}
              required
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* Color */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="color"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Color
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              id="color"
              placeholder="Color"
              value={color}
              onChange={(e) => setColor(e.target.value)}
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>

        {/* Uploaded Date */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="uploadedDate"
            className="w-1/4 text-[16px] font-normal text-[#333333]"
          >
            Uploaded Date*
          </label>
          <div className="w-3/4">
            <DateInput
                          name="uploadedDate"
                          className=" w-full"
                          id="uploadedDate"
                          value={uploadedDate}
                          onChange={(date) => setUploadedDate(date ? date.toISOString().split("T")[0] : "")}
                          placeholder="MM-DD-YYYY"
                        />
          </div>
        </div>

        <div className="flex gap-4 pb-4 justify-center">
          <Button
            type="cancel"
            label="Cancel"
            onClick={onClose}
            className="bg-gray-400 text-white"
          />
          <Button
            type="primary"
            label={loading ? "Adding..." : "Add"}
            disabled={loading}
            className="text-white"
          />
        </div>
      </form>
    </div>
  );
};

export default AddVehicleForm;
