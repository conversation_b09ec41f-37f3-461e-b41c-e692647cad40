import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import GenericTable from "../../GenericTable";
import AddAccessAreaForm from "./AddAccessAreaForm";
import ViewEditAccessAreaForm from "./ViewEditAccessAreaForm";
import { toast } from "react-toastify";
import Loader from "../../Loader.jsx";
// import deleted from "../../../Images/deleted.png";
// API functions
import { getAccessLevels, deleteAccessLevel } from "../../../api/facility";
import Delete from "../../../Images/Delete.svg";
import { FaPencil } from "react-icons/fa6";

const AccessAreas = () => {
  const { facilityId } = useParams();
  console.log("AccessAreas - facilityId from URL:", facilityId);

  // State hooks
  const [accessAreas, setAccessAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddAccessAreaForm, setShowAddAccessAreaForm] = useState(false);
  const [showViewAccessAreaForm, setShowViewAccessAreaForm] = useState(false);
  const [selectedAccessArea, setSelectedAccessArea] = useState(null);
  // Determines if the modal should open in edit mode directly
  const [defaultEditMode, setDefaultEditMode] = useState(false);

  // Fetch access levels whenever facilityId changes
  useEffect(() => {
    if (facilityId) {
      fetchAccessAreas();
    }
  }, [facilityId]);

  const fetchAccessAreas = async () => {
    setLoading(true);
    try {
      const response = await getAccessLevels(facilityId);
      // Adjust based on your API's response structure
      const data = response.data?.data || [];
      console.log("Fetched access areas:", data);
      setAccessAreas(data);
    } catch (error) {
      console.error("Error fetching access levels:", error);
      toast.error("Failed to fetch access levels.");
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    console.log("Add button clicked");
    setShowAddAccessAreaForm(true);
  };

  const handleCloseAddModal = () => {
    console.log("Closing Add Access Area modal");
    setShowAddAccessAreaForm(false);
  };

  const handleView = (row) => {
    console.log("Viewing access area:", row);
    setDefaultEditMode(false);
    setSelectedAccessArea(row);
    setShowViewAccessAreaForm(true);
  };

  const handleEdit = (row) => {
    console.log("Editing access area:", row);
    setDefaultEditMode(true);
    setSelectedAccessArea(row);
    setShowViewAccessAreaForm(true);
  };

  const handleCloseViewModal = () => {
    console.log("Closing View/Edit modal");
    setShowViewAccessAreaForm(false);
  };

  // Delete using API and refresh table
  const handleDelete = async (row) => {
    if (window.confirm("Are you sure you want to delete this access area?")) {
      try {
        await deleteAccessLevel(row.facility_access_level_id);
        toast.success("Access area deleted successfully!");
        fetchAccessAreas();
      } catch (error) {
        toast.error("Failed to delete access area.");
      }
    }
  };

  // Define table columns – clicking the building name opens the view/edit modal.
  const columns = [
    {
      name: "Name",
      selector: (row) => row.facility?.name,
    },
    {
      name: "System Name",
      selector: (row) => row.system_name, // Add System Name field
    },
    {
      name: "Area Type",
      selector: (row) => row.area_type, // Add Area Type field
    },
    {
      name: "Requestable for Guest",
      selector: (row) => (row.requestable_guest ? "Yes" : "No"), // Add Requestable for Guest field
    },
    {
      name: "Default Access for Guest",
      selector: (row) => (row.default_access_guest ? "Yes" : "No"), // Add Default Access for Guest field
    },
    {
      name: "Default Access for Identity",
      selector: (row) => (row.default_access_identity ? "Yes" : "No"), // Add Default Access for Identity field
    },
    {
      name: "Building",
      cell: (row) => (
        <span
          onClick={() => handleView(row)}
          style={{
            cursor: "pointer",
            // color: "blue",
            textDecoration: "underline",
          }}
        >
          {row.building?.name || "-"}
        </span>
      ),
    },
    {
      name: "Floor",
      selector: (row) => row.floor?.floor_number || "-",
    },
    {
      name: "Room No.",
      selector: (row) => row.room?.room_number || "-",
    },
    {
      name: "Actions",
      cell: (row) => (
        <div className="flex space-x-2">
          
          <img src={Delete} alt="Delete" className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDelete(row)} />
         
          <FaPencil className="p-1.5 bg-[#F0EDF5] text-[#4F2683] rounded-lg w-8 h-8 cursor-pointer" size={24} onClick={() => handleEdit(row)} />
        </div>
      ),
    },
  ];

  if (!facilityId) {
    return <div>Error: Facility ID is missing in URL parameters.</div>;
  }

  return (
    <div className="bg-white shadow-md rounded-lg">
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title="Access Areas (Access Levels)"
          searchTerm={searchTerm}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          onAdd={handleAdd}
          columns={columns}
          data={accessAreas}
          showSearch={true}
          showAddButton={true}
        />
      )}

      {/* Render Add Access Area modal */}
      {showAddAccessAreaForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white p-4 rounded-lg w-[80%]"> */}
            <AddAccessAreaForm
              facilityId={facilityId}
              onClose={handleCloseAddModal}
              refreshAccessAreas={fetchAccessAreas}
            />
          {/* </div> */}
        </div>
      )}

      {/* Render View/Edit Access Area modal */}
      {showViewAccessAreaForm && selectedAccessArea && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white shadow-lg p-4 rounded-lg w-[80%]"> */}
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditAccessAreaForm
                facilityId={facilityId}
                accessAreaData={selectedAccessArea}
                onClose={handleCloseViewModal}
                refreshAccessAreas={fetchAccessAreas}
                defaultEditMode={defaultEditMode}
              />
            </div>
          {/* </div> */}
        </div>
      )}
    </div>
  );
};

export default AccessAreas;
