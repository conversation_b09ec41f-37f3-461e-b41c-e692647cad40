import React, { useState, } from "react";
import GenericTable from "../../GenericTable";
import ViewEditTasksForm from "./ViewEditTasksForm";
import deleted from "../../../Images/Delete.svg"
import TruncatedRow from "../../Tooltip/TrucantedRow";
import TruncatedCell from "../../Tooltip/TruncatedCell";

const Tasks = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [tasks, setTasks] = useState([
    {
      id: 1,
      taskId: "T001",
      type: "Maintenance",
      requestId: "R1001",
      requestedBy: "<PERSON> Do<PERSON>",
      createdOn: "2023-03-01",
      justification: "Routine checkup and repair of equipment.",
      requestedFor: "Facility A",
      items: "Air filter, Oil",
      status: "Pending",
    },
    {
      id: 2,
      taskId: "T002",
      type: "Installation",
      requestId: "R1002",
      requestedBy: "<PERSON>",
      createdOn: "2023-03-05",
      justification: "Install new security system.",
      requestedFor: "Facility B",
      items: "CCTV, Alarm",
      status: "In Progress",
    },
    {
      id: 3,
      taskId: "T003",
      type: "Repair",
      requestId: "R1003",
      requestedBy: "Alice Johnson",
      createdOn: "2023-03-10",
      justification: "Fix broken window and door.",
      requestedFor: "Facility C",
      items: "Window glass, Door knob",
      status: "Completed",
    },
  ]);

  const [showViewTaskForm, setShowViewTaskForm] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  const handleDelete = (task) => {
    setTasks((prevTasks) => prevTasks.filter((t) => t.id !== task.id));
  };

  const handleView = (task) => {
    setSelectedTask(task);
    setShowViewTaskForm(true);
  };

  const handleUpdate = (updatedTask) => {
    setTasks((prevTasks) =>
      prevTasks.map((task) =>
        task.id === updatedTask.id ? updatedTask : task
      )
    );
    setShowViewTaskForm(false);
  };

  const columns = [
    {
      name: <TruncatedCell text="Task ID"/>,
      selector: (row) => row.taskId,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
         <TruncatedRow text={row.taskId}/>
        </span>
      ),
    },
    {
      name: <TruncatedCell text="Type"/>,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type}/>,
    },
    {
      name:<TruncatedCell text="Request ID"/>,
      selector: (row) => row.requestId,
      cell: (row) => <TruncatedRow text={row.requestId}/>,
    },
    {
      name: <TruncatedCell text="Requested By"/>,
      selector: (row) => row.requestedBy,
      cell: (row) => <TruncatedRow text={row.requestedBy}/>,
    },
    {
      name:<TruncatedCell text="Created On"/>,
      selector: (row) => row.createdOn,
      cell: (row) => <TruncatedRow text={row.createdOn}/>,
    },
    {
      name: <TruncatedCell text="Justification"/>,
      selector: (row) => row.justification,
      cell: (row) => <TruncatedRow text={row.justification} />,
    },
    {
      name:<TruncatedCell text="Requested For"/>,
      selector: (row) => row.requestedFor,
      cell: (row) => <TruncatedRow text={row.requestedFor}/>,
    },
    {
      name: <TruncatedCell text="Item(s)"/>,
      selector: (row) => row.items,
      cell: (row) => <TruncatedRow text={row.items} />,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${
            row.status.toLowerCase() === "completed"
              ? "bg-[#107C0F1A] bg-opacity-10 text-[#00BA00]"
              : "bg-[#E21B1B14] bg-opacity-8 text-[#D15858]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
       <img   src={deleted}
       alt="deleted"
       className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
       onClick={() => handleDelete(row)} />
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Tasks"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        data={tasks}
        showSearch={true}
        showAddButton={false} 
      />
      {showViewTaskForm && selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditTasksForm
                taskData={selectedTask}
                onClose={() => setShowViewTaskForm(false)}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default Tasks;
