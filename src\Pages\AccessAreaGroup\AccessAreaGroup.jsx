import React, { useState, useEffect } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import { IoFilter } from "react-icons/io5";
import FilterPanel from "../../Components/Observation/FilterPanel";
import newWindow from "../../Images/new-window.svg";
import { useNavigate, useLocation } from "react-router-dom";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import { AccessAreaGroupData } from "../../api/static";
import { useTranslation } from "react-i18next";

function AccessAreaGroup() {
  const { t } = useTranslation();
  const [tableData, setTableData] = useState(AccessAreaGroupData);
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const location = useLocation();
  const navigate = useNavigate();

  // If a new access group was passed from the form, add it at the top of the table data.
  useEffect(() => {
    if (location.state && location.state.newAccessGroup) {
      setTableData((prevData) => [location.state.newAccessGroup, ...prevData]);
    }
  }, [location.state, navigate, location.pathname]);

  // Filter table data based on the selected filter and search term
  const filteredData = tableData
    .filter((row) => (filter === "All" ? true : row.status === filter))
    .filter(
      (row) =>
        row.areaGroupName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (row.system &&
          row.system.toLowerCase().includes(searchTerm.toLowerCase())) ||
        row.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        row.creation.toLowerCase().includes(searchTerm.toLowerCase())
    );

  // Define table columns
  const columns = [
    {
      name: t("access_area_group.area_group_name"),
      selector: (row) => row.areaGroupName,
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => window.open(`/access-group-details`, "_blank")}
        >
          <span>{row.areaGroupName}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt="new window" />
          )}
        </div>
      ),
    },
    {
      name: t("access_area_group.type"),
      selector: (row) => row.type,
    },
    {
      name: t("access_area_group.creation"),
      selector: (row) => row.creation,
    },
    {
      name: <TruncatedCell text={t("access_area_group.requestable_in_self_service")} />,
      selector: (row) => row.requestable,
    },
    {
      name: t("access_area_group.status"),
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full `}
        >
          {t(`access_area_group.status_${row.status.toLowerCase()}`)}
        </span>
      ),
    },
  ];

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">
          {t("access_area_group.title")}
        </h2>
      </div>
      <div className="mb-4 flex items-center space-x-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: t("access_area_group.filter_all"), value: "All" },
            { label: t("access_area_group.filter_active"), value: "Active" },
            { label: t("access_area_group.filter_deleted"), value: "Deleted" },
          ]}
        />
      </div>
      <GenericTable
        title={t("access_area_group.title")}
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        onAdd={() => navigate(`/add-access-group-form`)}
        extraControls={
          <IoFilter
            className="bg-white shadow-sm border p-1 text-[#4F2683] h-8 w-8 rounded cursor-pointer"
            onClick={() => setIsFilterPanelOpen(true)}
          />
        }
        data={filteredData}
        fixedHeader
        fixedHeaderScrollHeight="440px"
      />
      <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
    </div>
  );
}

export default AccessAreaGroup;
