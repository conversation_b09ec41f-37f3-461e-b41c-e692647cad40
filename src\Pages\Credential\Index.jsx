import React, { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import GenericTable from "../../Components/GenericTable";
import PrintModalBothSide from "./PrintModalBothSide";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import ChatModal from "./ChatModal";
import newWindow from "../../Images/new-window.svg"; 
// Images
import editIcon from "../../Images/EditIcon.svg";
import Cemara from "../../Images/camera.svg";
import PrintIcon from "../../Images/print.svg";
import returnIcon from "../../Images/returnIcon.svg";
import demo from "../../Images/demoimg.svg"
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import TruncatedRow from "../../Components/Tooltip/TrucantedRow";
import { FilterButtons } from "../../Components/GenericTable"; 
import AddCredentialModal from "../../Components/Credential/Credential/CredentialAdd";

// Initial credentials data
const initialCredentials = [
  {
    pin:"123",
    cardType:"Permanent Card",
    id: 1,
    name: "ADAM L'THELAN",
    eid: "300074045",
    type: "Employee",
    badgeTemplate: "Employee Badge Template",
    createdOn: "20-Mar-2025 12:00 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "Beijing",
    shipmentAddress: "Karnataka, India",
    status: "Unprinted",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"

  },
  {
    pin:"123",
    cardType:"Permanent Card",
    id: 2,
    name: "BRIAN SMITH",
    eid: "300074046",
    type: "Contractor",
    badgeTemplate: "Contractor Badge Template",
    createdOn: "21-Mar-2025 10:00 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "New York",
    shipmentAddress: "New York, USA",
    status: "Printed",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"

  },
  {
    pin:"123",
    cardType:"Permanent Card",
    id: 3,
    name: "CHARLIE JOHNSON",
    eid: "300074047",
    type: "Employee",
    badgeTemplate: "Employee Badge Template",
    createdOn: "22-Mar-2025 11:00 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "London",
    shipmentAddress: "London, UK",
    status: "Pending Photo Upload",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"

  },
  {
    pin:"123",
    cardType:"Permanent",
    id: 4,
    name: "DAVID LEE",
    eid: "*********",
    type: "Contractor",
    badgeTemplate: "Contractor Badge Template",
    createdOn: "23-Mar-2025 09:30 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "Sydney",
    shipmentAddress: "Sydney, Australia",
    status: "Badges in Queue",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"

  },
  {
    pin:"123",
    cardType:"Permanent",
    id: 5,
    name: "ELLA MARTIN",
    eid: "*********",
    type: "Employee",
    badgeTemplate: "Employee Badge Template",
    createdOn: "24-Mar-2025 08:45 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "Toronto",
    shipmentAddress: "Toronto, Canada",
    status: "Completed",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"
  },
  {
    pin:"123",
    cardType:"Permanent",
    id: 6,
    name: "ELLA MARTIN",
    eid: "*********",
    type: "Employee",
    badgeTemplate: "Employee Badge Template",
    createdOn: "24-Mar-2025 08:45 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "Toronto",
    shipmentAddress: "Toronto, Canada",
    status: "Completed Photo Upload",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"
  },
  {
    pin:"123",
    cardType:"Permanent",
    id: 7,
    name: "ELLA MARTIN",
    eid: "*********",
    type: "Employee",
    badgeTemplate: "Employee Badge Template",
    createdOn: "24-Mar-2025 08:45 AM",
    decreatedOn:"20-Mar-2025 12:00 AM",
    facilityCode: "Toronto",
    shipmentAddress: "Toronto, Canada",
    status: "Print Failed Badges",
    image: demo,
    cardFormat:"Multitech General",
    cardNumber:"1234",
    reason:"Card Lost"
  },
];

const CredentialHub = () => {
  const { t } = useTranslation();
  const [activeStatus, setActiveStatus] = useState('');
  const [searchTerm, setSearchTerm] = useState("");
  const [credentials, setCredentials] = useState(initialCredentials);
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedCredential, setSelectedCredential] = useState(null);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editCredential, setEditCredential] = useState(null); 
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);
  const [chatCredential, setChatCredential] = useState(null);
  const [hoveredRow, setHoveredRow] = useState(null);
  const statusOptions = [
    { label: t('credential_hub.status_unprinted'), value: 'Unprinted' },
    { label: t('credential_hub.status_printed'), value: 'Printed' },
    { label: t('credential_hub.status_in_queue'), value: 'Badges in Queue' },
    { label: t('credential_hub.status_completed'), value: 'Completed' },
    { label: t('credential_hub.status_pending_photo_upload'), value: 'Pending Photo Upload' },
    { label: t('credential_hub.status_completed_photo_upload'), value: 'Completed Photo Upload' },
    { label: t('credential_hub.status_print_failed_badges'), value: 'Print Failed Badges' },
  ];
  const filteredData = useMemo(() => {
    return credentials.filter((item) => item.status === activeStatus);
  }, [activeStatus, credentials]);

  const openEditModal = (id) => {
    const cred = credentials.find((c) => c.id === id);
    if (cred) {
      setSelectedCredential(cred);
      setIsEditModalOpen(true);
    }
  };

  const handlePrintClick = (cred) => {
    setSelectedCredential(cred);
    setPrintModalVisible(true);
  };

  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedCredential(null);
  };

  const handlePhotoSave = (imageData) => {
    setCredentials((prev) =>
      prev.map((c) =>
        c.id === selectedCredential.id ? { ...c, image: imageData } : c
      )
    );
    setIsEditModalOpen(false);
    setSelectedCredential(null);
  };

  const closeChatModal = () => {
    setIsChatModalOpen(false);
    setChatCredential(null);
  };

  const handleAddOrEditCredential = (credentialData) => {
    if (editCredential) {
      setCredentials((prev) =>
        prev.map((cred) =>
          cred.id === editCredential.id ? { ...cred, ...credentialData } : cred
        )
      );
    } else {
      credentialData.id = credentials.length + 1;
      setCredentials([credentialData, ...credentials]);
    }
    setIsAddModalOpen(false);
    setEditCredential(null);
  };

  const columns = [
    {
      name: t('credential_hub.name'),
      selector: (row) => row.name,
      sortable: true,
      cell: (row, index) => (
        <div
          className="flex items-center gap-2 cursor-pointer group w-auto"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => window.open(`/credential-details`, "_blank")}
        >
          <img src={row.image} alt={row.name} className="w-6 h-6 rounded-full" />
          <span>{row.name}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt="new window" />
          )}
        </div>
      ),
      width: "17%",
    },
    { 
      name: t('credential_hub.eid'),
      selector: (row) => row.eid,
      cell: (row) => (<TruncatedRow text={row.eid}/>),
    },
    { 
      name: t('credential_hub.type'),
      selector: (row) => row.type,
      cell: (row) => (<TruncatedRow text={row.type}/>),
    },
    { 
      name: <TruncatedCell text={t('credential_hub.badge_template')}/>,
      selector: (row) => row.badgeTemplate,
      cell: (row) => (<TruncatedRow text={row.badgeTemplate}/>),
    },
    { 
      name: <TruncatedCell text={t('credential_hub.created_on')}/>,
      selector: (row) => row.createdOn,
      cell: (row) => (<TruncatedRow text={row.createdOn}/>),
    },
    { 
      name: <TruncatedCell text={t('credential_hub.badging_facility')}/>,
      selector: (row) => row.facilityCode,
      cell: (row) => (<TruncatedRow text={row.facilityCode}/>),
    },
    { 
      name: <TruncatedCell text={t('credential_hub.shipment_address')}/>,
      selector: (row) => row.shipmentAddress,
      cell: (row) => (<TruncatedRow text={row.shipmentAddress}/>),
    },
    {
      name: t('credential_hub.status'),
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`px-4 py-1 rounded-full whitespace-nowrap overflow-hidden text-ellipsis ${
            row.status === "Unprinted" ||
            row.status === "Print Failed Badges"
              ? "bg-[#E21B1B14] text-[#D15858]"
              : "bg-[#107C0F1A] text-[#00BA00]"
          }`}
        >
          <TruncatedRow text={t(`credential_hub.status_${row.status.replace(/\s+/g, '_').toLowerCase()}`)}/>
        </span>
      ),
      width: "10%"
    },
    {
      name: t('credential_hub.action'),
      cell: (row) => (
        <div className="flex justify-center items-center space-x-2">
          <img
            src={Cemara}
            alt="Camera"
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => openEditModal(row.id)}
          />
          <img
            src={PrintIcon}
            alt="Print"
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handlePrintClick(row)}
          />
          <img
            src={editIcon}
            alt="Edit"
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => {
              setEditCredential(row); 
              setIsAddModalOpen(true);
            }}
          />
          <img
            src={returnIcon}
            alt="Return"
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => console.log("Return clicked for:", row)}
          />
        </div>
      ),
      center: true,
      width: "15%"
    },
  ];

  return (
    <div className="pl-24 mt-20 pr-8 h-full">
      <div className="text-[24px] font-normal text-[#4F2683] mb-4">
        <h3>{t('credential_hub.title')}</h3>
      </div>
      <div className="mb-4">
        <FilterButtons
          filter={activeStatus}
          onFilterChange={setActiveStatus}
          filterOptions={statusOptions}
        />
      </div>
      <div className="mt-4">
        <GenericTable
          title={t('credential_hub.table_title')}
          searchTerm={searchTerm}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          columns={columns}
          data={filteredData}
          onAdd={() => {
            setEditCredential(null);
            setIsAddModalOpen(true);
          }}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover={false}
          striped
        />
      </div>
      {printModalVisible && selectedCredential && (
        <PrintModalBothSide
          credential={{
            ...selectedCredential,
            image: selectedCredential.image || "",
          }}
          onClose={handleClosePrintModal}
        />
      )}
      {isEditModalOpen && (
        <EditPhotoModal
          onClose={() => {
            setIsEditModalOpen(false);
            setSelectedCredential(null);
          }}
          onSave={handlePhotoSave}
        />
      )}
      {isAddModalOpen && (
        <AddCredentialModal
        initialValues={
          editCredential
            ? {
                ...editCredential,
                searchIdentity: {
                  name: editCredential.name,
                  eid: editCredential.eid,
                  type: editCredential.type,
                  image: editCredential.image,
                },
              }
            : null
        }
          onClose={() => {
            setIsAddModalOpen(false);
            setEditCredential(null);
          }}
          onAdd={handleAddOrEditCredential}
        />
      )}
      {isChatModalOpen && chatCredential && (
        <ChatModal onClose={closeChatModal} credential={chatCredential} />
      )}
    </div>
  );
};

export default CredentialHub;
