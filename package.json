{"name": "reception-desk", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^7.28.3", "@reduxjs/toolkit": "^2.6.0", "axios": "^1.8.1", "cra-template": "1.2.0", "date-fns": "^2.28.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "i18next": "^25.2.1", "moment": "^2.30.1", "primeicons": "^7.0.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-clock": "^5.1.0", "react-data-table-component": "^7.6.2", "react-datepicker": "^8.0.0", "react-datetime": "^3.3.1", "react-datetime-picker": "^6.0.1", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-modal": "^3.16.3", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "react-signature-canvas": "^1.0.7", "react-time-picker": "^7.0.0", "react-toastify": "^11.0.3", "react-tooltip": "^5.28.0", "react-webcam": "^7.2.0", "redux-thunk": "^3.1.0", "styled-components": "^6.1.18", "sweetalert2": "^11.16.0", "sweetalert2-react-content": "^5.1.0", "web-vitals": "^4.2.4", "yup": "^1.6.1"}, "scripts": {"start": "set PORT=3005 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}}