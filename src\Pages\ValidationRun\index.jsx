import React, { useState } from 'react';
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import { IoFilter } from "react-icons/io5";
import { validationRun } from '../../api/static';
import TruncatedCell from '../../Components/Tooltip/TruncatedCell';
import TruncatedRow from '../../Components/Tooltip/TrucantedRow';
import FilterPanel from '../../Components/Observation/FilterPanel';


const ValidationRun = () => {
  // State to manage filtered data
  const [filteredData, setFilteredData] = useState(validationRun);
  const [activeFilter, setActiveFilter] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
   // Filter panel toggle
    const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  // Columns for the table
  const columns = [
    {
      name: <TruncatedCell text="Name" />,
      selector: row => row.validationName,
      cell: row => <TruncatedRow text={row.validationName} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Run ID" />,
      selector: row => row.runId,
      cell: row => <TruncatedRow text={row.runId} />,
    },
    {
      name: <TruncatedCell text="Validation Type" />,
      selector: row => row.validationType,
      cell: row => <TruncatedRow text={row.validationType} />,
    },
    {
      name: "Assigned",
      selector: row => row.assigned,
      cell: row => <TruncatedRow text={row.assigned} />,
    },
    {
        name: "Status",
        selector: row => row.status,
        sortable: true,
        cell: row => (
          <span
            className={`w-24 p-1 flex justify-center items-center rounded-full ${row.status.toLowerCase() === "active"
                ? "bg-[#107C0F1A] bg-opacity-10 text-[#00BA00]"
                : "bg-[#E21B1B14] bg-opacity-8 text-[#D15858]"
              }`}
          >
            {row.status}
          </span>
        ),
        center: true,
      },
    {
      name: "Start Date",
      selector: row => row.startDate,
    },
    {
      name: "End Date",
      selector: row => row.endDate,
      center: true,
    },
    {
        name: <TruncatedCell text="Completed"/>,
        selector: row => row.complete,
        cell: row => <TruncatedRow text={row.complete} />,
    },
    {
        name : <TruncatedCell text="Pending"/>,
        selector: row => row.pending,
        cell: row => <TruncatedRow text={row.pending} />,
    },
    {
        name: <TruncatedCell text="Total"/>,
        selector: row => row.total,
        cell: row => <TruncatedRow text={row.total} />,
    }
    
  ];

  // Search handler
  const handleSearch = e => {
    const query = e.target.value.toLowerCase();
    setSearchQuery(query);
    setFilteredData(
      validationRun.filter(item =>
        item.validationName.toLowerCase().includes(query)
      )
    );
  };

  const handleFilterChange = filter => {
    setActiveFilter(filter);
    if (filter === "All") {
      setFilteredData(validationRun);
    } else if (filter === "Active") {
      setFilteredData(validationRun.filter(item => item.status === "Active"));
    }
  };

  return (
    <div className="pt-20 pl-24">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">
          Validation Run
        </h2>
      </div>

      {/* Filter Buttons */}
      <div className="flex justify-start mb-4">
        <FilterButtons
          filter={activeFilter}
          onFilterChange={handleFilterChange}
          filterOptions={[
            { label: "All", value: "All" },
            { label: "Active", value: "Active" },
          ]}
        />
      </div>

      {/* Table */}
      <div className="bg-white rounded-[10px]">
        <GenericTable
          fixedHeaderScrollHeight="400"
          title="Validation Run"
          searchTerm={searchQuery}
          onSearchChange={handleSearch}
          columns={columns}
          data={filteredData}
          showSearch={true}
          showAddButton={false}
          extraControls={
                    <IoFilter
                      className="bg-white shadow-sm border p-1 text-[#4F2683] h-8 w-8 rounded cursor-pointer"
                      onClick={() => setIsFilterPanelOpen(true)}
                    />
                  }
        />
        <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
      </div>
    </div>
  );
};

export default ValidationRun;
