import React, { useState, useMemo, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { toast } from "react-toastify";
import { updateTraining } from "../../../api/identity";

const trainingFields = [
  { label: "Name *", type: "text", placeholder: "Name", name: "name" },
  { label: "Course Number *", type: "text", placeholder: "Course Number", name: "course_number" },
  { label: "Category *", type: "text", placeholder: "Category", name: "category" },
  { label: "Course Type *", type: "dropdown", placeholder: "Course Type", name: "course_type" },
  { label: "Recurrence *", type: "dropdown", placeholder: "Recurrence", name: "recurrence" },
  { label: "Due Date *", type: "date", placeholder: "Due Date", name: "due_date" },
  { label: "Date Completed", type: "date", placeholder: "Date Completed", name: "date_completed" },
  { label: "Score", type: "number", placeholder: "Score", name: "score" },
  { label: "Status *", type: "dropdown", placeholder: "Status", name: "status" },
];

const ViewEditTrainingForm = ({ onClose, trainingData, onUpdate }) => {
  const trainingSchema = useMemo(
    () =>
      yup.object().shape({
        name: yup.string().required("Name is required"),
        course_number: yup.string().required("Course Number is required"),
        category: yup.string().required("Category is required"),
        course_type: yup.number().required("Course Type is required"),
        recurrence: yup.number().required("Recurrence is required"),
        due_date: yup.date().required("Due Date is required"),
        date_completed: yup.date().nullable(),
        score: yup.number().nullable(),
        status: yup.number().required("Status is required"),
      }),
    []
  );

  const {
    register,
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(trainingSchema),
    defaultValues: trainingData,
  });

  useEffect(() => {
    reset(trainingData);
  }, [trainingData, reset]);

  const [loading, setLoading] = useState(false);

  // Define dropdown options
  const statusOptions = [
    { label: "Pass", value: 1 },
    { label: "Fail", value: 2 },
    { label: "Pending", value: 3 },
  ];

  const courseTypeOptions = [
    { label: "Online", value: 1 },
    { label: "Offline", value: 2 },
    { label: "Hybrid", value: 3 },
  ];

  const recurranceOptions = [
    { label: "One Time", value: 1 },
    { label: "Monthly", value: 2 },
    { label: "Quarterly", value: 3 },
    { label: "Yearly", value: 4 },
  ];

  const submitFormHandler = async (data) => {
    setLoading(true);
    try {
      const processedData = {
        name: data.name,
        course_number: data.course_number,
        category: data.category,
        course_type: parseInt(data.course_type),
        recurrence: parseInt(data.recurrence),
        status: parseInt(data.status),
        score: data.score ? parseInt(data.score) : null,
        due_date: data.due_date ? (typeof data.due_date === 'string' ? data.due_date : data.due_date.toISOString().split("T")[0]) : null,
        date_completed: data.date_completed ? (typeof data.date_completed === 'string' ? data.date_completed : data.date_completed.toISOString().split("T")[0]) : null,
      };

      // Call API to update training
      const result = await updateTraining(trainingData.training_id || trainingData.id, processedData);

      // Update local state
      onUpdate({ ...trainingData, ...processedData });
      toast.success("Training updated successfully!");
      onClose();
    } catch (error) {
      console.error("Error updating training:", error);
      toast.error("Failed to update training. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full p-0">
      <div className="flex items-center mb-2 px-4 pt-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Edit Training</h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSubmit(submitFormHandler)} className="bg-white p-6 rounded-lg shadow-lg">
        <h2 className="text-[20px] text-[#333333] font-medium pb-4">Training Details</h2>
        {trainingFields.map(({ label, type, name, placeholder }, idx) => (
          <div key={idx} className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">{label}</label>
            <div className="w-3/4">
              {(name === "status" || name === "course_type" || name === "recurrence") ? (
                <Controller
                  name={name}
                  control={control}
                  render={({ field: { onChange, value }, fieldState: { error } }) => {
                    let options = [];
                    if (name === "status") {
                      options = statusOptions;
                    } else if (name === "course_type") {
                      options = courseTypeOptions;
                    } else if (name === "recurrence") {
                      options = recurranceOptions;
                    }
                    return (
                      <CustomDropdown
                      className="h-10"
                        options={options}
                        value={value}
                        onSelect={(val) => onChange(val)}
                        placeholder={placeholder}
                        error={error}
                      />
                    );
                  }}
                />
              ) : type === "date" ? (
                <Controller
                  control={control}
                  name={name}
                  render={({ field }) => (
                    <DateInput
                      value={field.value}
                      onChange={(date) => field.onChange(date)}
                      placeholder={placeholder}
                      error={errors[name]}
                      className="w-full"
                    />
                  )}
                />
              ) : (
                <>
                  <Input type={type} name={name} placeholder={placeholder} error={errors[name]} {...register(name)} />
                  {errors[name] && <p className="text-red-500 text-sm mt-1">{errors[name].message}</p>}
                </>
              )}
            </div>
          </div>
        ))}
        <div className="flex justify-center gap-4 mt-6">
          <Button type="cancel" label="Cancel" onClick={onClose} />
          <Button type="primary" label={loading ? "Saving..." : "Update"} disabled={loading} />
        </div>
      </form>
    </div>
  );
};

export default ViewEditTrainingForm;
