import React, { useState } from "react";
import Button from "../Global/Button";

const OwnersAdd = ({ onSubmit, onClose, availableOwners }) => {
  const defaultOwners = [
    { name: "<PERSON>", eid: "E001", type: "Employee", organization: "Org1", jobTitle: "Developer", status: "Active" },
    { name: "<PERSON>", eid: "E002", type: "Employee", organization: "Org1", jobTitle: "Designer", status: "Inactive" },
    { name: "<PERSON>", eid: "E003", type: "Manager", organization: "Org2", jobTitle: "Project Manager", status: "Active" },
    { name: "<PERSON>", eid: "E004", type: "Employee", organization: "Org2", jobTitle: "QA Engineer", status: "Active" },
    { name: "<PERSON>", eid: "E005", type: "Employee", organization: "Org3", jobTitle: "Support", status: "Inactive" },
    { name: "<PERSON>", eid: "E006", type: "Employee", organization: "Org4", jobTitle: "Analyst", status: "Active" },
    { name: "<PERSON>", eid: "E007", type: "Manager", organization: "Org4", jobTitle: "Team Lead", status: "Active" },
    { name: "Fiona Blue", eid: "E008", type: "Employee", organization: "Org5", jobTitle: "Consultant", status: "Active" },
  ];

  const ownersData = availableOwners && availableOwners.length ? availableOwners : defaultOwners;

  // We'll store the entire owner object when one is selected.
  const [selectedOwner, setSelectedOwner] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);

  // Filter owners by matching search term with name, type, or eid.
  const filteredOwners = ownersData.filter(owner => {
    const term = searchTerm.toLowerCase();
    return (
      owner.name.toLowerCase().includes(term) ||
      owner.type.toLowerCase().includes(term) ||
      owner.eid.toLowerCase().includes(term)
    );
  });

  const handleOwnerInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);
    setIsDropdownVisible(value.length > 0);
    // Clear the previously selected owner if user starts typing
    if (selectedOwner && !value.includes(selectedOwner.eid)) {
      setSelectedOwner(null);
    }
  };

  const handleOwnerSelect = (owner) => {
    setSelectedOwner(owner);
    setSearchTerm(`${owner.name} - ${owner.type} - ${owner.eid}`);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedOwner) {
      alert("Please select an owner.");
      return;
    }
    onSubmit(selectedOwner);
    onClose();
  };

  return (
    <div className="w-full p-0">
      {/* Header */}
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Add Owners</h2>
        <button
          className="w-8 h-8 bg-[#4F2683] flex justify-center items-center p-0 text-white text-2xl rounded-full"
          onClick={onClose}
          type="button"
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
        {/* Owner Search and Dropdown */}
        <div className="flex items-center mb-4">
          <label htmlFor="owner" className="text-[16px] font-normal w-1/4">
            Select Owner*
          </label>
          <div className="relative w-3/4">
            <input
              type="text"
              id="owner"
              placeholder="Search Owner"
              value={searchTerm}
              onChange={handleOwnerInputChange}
              onFocus={() => setIsDropdownVisible(true)}
              onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
              className="w-full h-11 border border-gray-300 rounded px-3"
            />
            {isDropdownVisible && (
              <div className="absolute w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-10">
                {filteredOwners.length > 0 ? (
                  filteredOwners.map((owner) => (
                    <div
                      key={owner.eid}
                      className="p-2 cursor-pointer hover:bg-gray-100"
                      onMouseDown={() => handleOwnerSelect(owner)}
                    >
                      {owner.name} - {owner.type} - {owner.eid}
                    </div>
                  ))
                ) : (
                  <div className="p-2 text-gray-700 text-center">
                    No Results Found.
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <Button
            type="button"
            label="Cancel"
            onClick={onClose}
            className="bg-gray-400 text-white"
          />
          <Button
            type="submit"
            label="Add"
            className="bg-[#4F2683] text-white"
          />
        </div>
      </form>
    </div>
  );
};

export default OwnersAdd;
