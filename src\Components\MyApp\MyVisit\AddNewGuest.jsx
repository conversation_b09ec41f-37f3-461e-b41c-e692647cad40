import React from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Input from "../../Global/Input/Input";
import Button from "../../Global/Button";
import SearchableDropdown from "../../Global/SearchableDropdownIdentity";
import CustomDropdown from "../../Global/CustomDropdown";


const AddNewGuest = ({ onClose, onAdd, }) => {

  const validationSchema = Yup.object().shape({
    firstName: Yup.string().required("First Name required"),
    lastName: Yup.string().required("Last Name is required"),
    email: Yup.string().required("Email is required"),
  });

  const handleSubmit = (values) => {
    const newGuest = {
      firstName: values.firstName,
      lastName: values.lastName,
      email: values.email,
      mobile: values.mobile,
      company: values.company,
      privateVisitor: values.privateVisitor,
    };

    onAdd(newGuest);
  };
  // Default form values
  const initialValues = {
    firstName: "",
    lastName: "",
    email: "",
    mobile: "",
    privateVisitor: "",
    // company: "" (if needed)
  };

  const PrivateVisitorOptions =[
    {label:"Yes" , value:"Yes"},
    {label:"No" , value:"No"},
  ]
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-6">
      <div className="bg-white w-full h-auto max-w-5xl p-4 rounded-lg shadow-lg overflow-y-auto">
        <div className="flex justify-between items-center">
          <h2 className="text-[30px] text-[#4F2683] font-normal">
            Create New Guest
          </h2>
          <button
            onClick={onClose}
            className="flex items-center justify-center bg-[#4F2683] text-white text-2xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
          >
            &times;
          </button>
        </div>
        <hr className="mb-4 mt-2" />
        <Formik
          enableReinitialize
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          initialValues={initialValues}
        >
          {({ values, errors, touched, setFieldValue }) => (
            <Form className="space-y-4">
              <div className="space-y-4">
                {/* First Name Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">First Name *</label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="firstName"
                      placeholder="Last Name"
                      value={values.firstName}
                      onChange={(e) => setFieldValue("firstName", e.target.value)}
                    />
                    {touched.firstName && errors.firstName && (
                      <div className="text-red-500 text-sm">{errors.firstName}</div>
                    )}
                  </div>
                </div>
                {/* Last Name Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Last Name *</label>
                  <div className="w-2/3">
                    <Input
                      type="text"
                      name="lastName"
                      placeholder="Last Name"
                      value={values.lastName}
                      onChange={(e) => setFieldValue("lastName", e.target.value)}
                    />
                    {touched.lastName && errors.lastName && (
                      <div className="text-red-500 text-sm">{errors.lastName}</div>
                    )}
                  </div>
                </div>
                {/* Email Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Email *</label>
                  <div className="w-2/3">
                    <Input
                      type="email"
                      name="email"
                      placeholder="Email"
                      value={values.email}
                      onChange={(e) => setFieldValue("email", e.target.value)}
                    />
                    {touched.email && errors.email && (
                      <div className="text-red-500 text-sm">{errors.email}</div>
                    )}
                  </div>
                </div>
                {/* Mobile Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Mobile Phone</label>
                  <div className="w-2/3">
                    <Input
                      type="number"
                      name="mobile"
                      placeholder="Mobile "
                      value={values.mobile}
                      onChange={(e) => setFieldValue("mobile", e.target.value)}
                    />
                    {touched.mobile && errors.mobile && (
                      <div className="text-red-500 text-sm">{errors.mobile}</div>
                    )}
                  </div>
                </div>
                {/* Private type Field */}
                <div className="flex mb-2 items-center">
                  <label className="mr-2 w-1/3">Private Visitor?</label>
                  <div className="w-2/3">
                    <CustomDropdown
                      options={PrivateVisitorOptions}
                      placeholder="Yes/No"
                      value={values.privateVisitor}
                      className="h-10"
                      onSelect={(selectedValue) =>
                        setFieldValue("privateVisitor", selectedValue)
                      }
                      error={touched.privateVisitor && errors.privateVisitor ? { message: errors.privateVisitor } : null}
                    />
                  </div>
                </div>
                
              </div>
              <div className="flex justify-center gap-4 mt-6">
                <Button type="cancel" label="Cancel" onClick={onClose} />
                <Button type="primary" label="Save" buttonType="submit" />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default AddNewGuest;
