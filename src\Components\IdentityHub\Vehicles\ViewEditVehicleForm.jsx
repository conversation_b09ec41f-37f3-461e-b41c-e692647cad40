import React, { useState } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";

const ViewEditVehicleForm = ({ vehicleData, onUpdate, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);

  // Safety check to ensure vehicleData exists
  const safeVehicleData = vehicleData || {};

  const [formData, setFormData] = useState({
    id: safeVehicleData.vehicle_id || safeVehicleData.id || "",
    plateNumber: safeVehicleData.plateNumber || safeVehicleData.plate_number || "",
    issuedBy: safeVehicleData.issuedBy || safeVehicleData.issued_by || "",
    VIN: safeVehicleData.VIN || safeVehicleData.vin || "",
    year: safeVehicleData.year || "",
    make: safeVehicleData.make || "",
    model: safeVehicleData.model || "",
    color: safeVehicleData.color || "",
    uploadedDate: safeVehicleData.uploadedDate || safeVehicleData.uploaded_date || safeVehicleData.created_at || "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = (e) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditMode(false);
  };

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  return (
    <div className="w-full p-2">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Vehicle Details
        </h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
        {/* Plate Number */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="plateNumber"
            className="w-1/4 text-[16px] font-normal"
          >
            Plate Number
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="plateNumber"
              id="plateNumber"
              value={formData.plateNumber}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Issued by */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="issuedBy"
            className="w-1/4 text-[16px] font-normal"
          >
            Issued by
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="issuedBy"
              id="issuedBy"
              value={formData.issuedBy}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* VIN */}
        <div className="flex items-center mb-4">
          <label htmlFor="VIN" className="w-1/4 text-[16px] font-normal">
            VIN
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="VIN"
              id="VIN"
              value={formData.VIN}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Year */}
        <div className="flex items-center mb-4">
          <label htmlFor="year" className="w-1/4 text-[16px] font-normal">
            Year
          </label>
          <div className="w-3/4">
            <Input
              type="number"
              name="year"
              id="year"
              value={formData.year}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Make */}
        <div className="flex items-center mb-4">
          <label htmlFor="make" className="w-1/4 text-[16px] font-normal">
            Make
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="make"
              id="make"
              value={formData.make}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Model */}
        <div className="flex items-center mb-4">
          <label htmlFor="model" className="w-1/4 text-[16px] font-normal">
            Model
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="model"
              id="model"
              value={formData.model}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Color */}
        <div className="flex items-center mb-4">
          <label htmlFor="color" className="w-1/4 text-[16px] font-normal">
            Color
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="color"
              id="color"
              value={formData.color}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        {/* Uploaded Date */}
        <div className="flex items-center mb-4">
          <label
            htmlFor="uploadedDate"
            className="w-1/4 text-[16px] font-normal"
          >
            Uploaded Date
          </label>
          <div className="w-3/4">
            <Input
              type="date"
              name="uploadedDate"
              id="uploadedDate"
              value={formData.uploadedDate}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        <div className="flex gap-4 justify-end">
          {!isEditMode ? (
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                setIsEditMode(true);
              }}
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Edit
            </button>
          ) : (
            <>
              <button
                type="button"
                onClick={() => {
                  setIsEditMode(false);
                  setFormData({
                    id: safeVehicleData.vehicle_id || safeVehicleData.id || "",
                    plateNumber: safeVehicleData.plateNumber || safeVehicleData.plate_number || "",
                    issuedBy: safeVehicleData.issuedBy || safeVehicleData.issued_by || "",
                    VIN: safeVehicleData.VIN || safeVehicleData.vin || "",
                    year: safeVehicleData.year || "",
                    make: safeVehicleData.make || "",
                    model: safeVehicleData.model || "",
                    color: safeVehicleData.color || "",
                    uploadedDate: safeVehicleData.uploadedDate || safeVehicleData.uploaded_date || safeVehicleData.created_at || "",
                  });
                }}
                className="px-4 py-2 bg-gray-400 text-white rounded"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Save
              </button>
            </>
          )}
        </div>
      </form>
    </div>
  );
};

export default ViewEditVehicleForm;
