import React, { useState } from "react";
import Button from "../Global/Button";
import Input from "../Global/Input/Input";
import { createGuest } from "../../api/guest";
import { toast } from "react-toastify";
import { useSelector } from "react-redux";
import CustomDropdown from "../Global/CustomDropdown";

const AddGuestForm = ({ onClose, onSuccess}) => {
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        mobilePhone: "",
        company: "",
        isPrivate: "No",
    });
    const [loading, setLoading] = useState(false);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

     const selectedFacility = useSelector(state => state.facility.selectedFacility);

    const handleSubmit = async (e) => {
        e.preventDefault();

        // Basic validation
        if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
            toast.error("Please fill in all required fields (First Name, Last Name, and Email).");
            return;
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email.trim())) {
            toast.error("Please enter a valid email address.");
            return;
        }

        setLoading(true);
        try {
            const guestData = {
                first_name: formData.firstName.trim(),
                last_name: formData.lastName.trim(),
                email: formData.email.trim(),
                mobile_phone: formData.mobilePhone.trim() || null,
                company: formData.company.trim() || null,
                private_visitor: formData.isPrivate === "Yes",
            //   facility_id: selectedFacility?.id || null
            };

            console.log("Sending guest data:", guestData);

            try {
                await createGuest(guestData);
                toast.success("Guest added successfully!");
            } catch (apiError) {
                console.error("API Error:", apiError);
                console.error("API Error Response:", apiError.response?.data);

                if (apiError.response?.status === 401) {
                    toast.error("Please login to add guests.");
                } else if (apiError.response?.status === 400) {
                    const errorMessage = apiError.response?.data?.message || "Invalid guest data. Please check your inputs.";
                    toast.error(errorMessage);
                } else if (apiError.response?.status === 409) {
                    toast.error("A guest with this email already exists.");
                } else {
                    const errorMessage = apiError.response?.data?.message || "Failed to add guest";
                    toast.error(errorMessage);
                }
            }
            onSuccess();
        } catch (error) {
            console.error("Error adding guest:", error);
            toast.error("Failed to add guest. Please try again.");
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg">
                <div className="flex items-center mb-2 px-4 pt-2 justify-between">
                    <h2 className="text-[30px] font-normal text-[#4F2683]">Add Guest</h2>
                    <button
                        className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
                        type="button"
                        onClick={onClose}
                    >
                        &times;
                    </button>
                </div>
                <hr className="mx-3" />
                <form
                    onSubmit={handleSubmit}
                    className="bg-white p-6 rounded-lg shadow-lg"
                >
                    <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                        Guest Details
                    </h2>
                    <div className="flex items-center mb-4">
                        <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                            First Name *
                        </label>
                        <div className="w-3/4">
                            <Input
                                type="text"
                                name="firstName"
                                placeholder="First Name"
                                value={formData.firstName}
                                onChange={handleChange}
                                required
                            />
                        </div>
                    </div>
                    <div className="flex items-center mb-4">
                        <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                            Last Name *
                        </label>
                        <div className="w-3/4">
                            <Input
                                type="text"
                                name="lastName"
                                placeholder="Last Name"
                                value={formData.lastName}
                                onChange={handleChange}
                                required
                            />
                        </div>
                    </div>
                    <div className="flex items-center mb-4">
                        <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                            Email *
                        </label>
                        <div className="w-3/4">
                            <Input
                                type="email"
                                name="email"
                                placeholder="Email"
                                value={formData.email}
                                onChange={handleChange}
                                required
                            />
                        </div>
                    </div>
                    <div className="flex items-center mb-4">
                        <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                            Mobile Phone
                        </label>
                        <div className="w-3/4">
                            <Input
                                type="tel"
                                name="mobilePhone"
                                placeholder="Mobile Phone"
                                value={formData.mobilePhone}
                                onChange={handleChange}
                            />
                        </div>
                    </div>
                    <div className="flex items-center mb-4">
                        <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                            Company
                        </label>
                        <div className="w-3/4">
                            <Input
                                type="text"
                                name="company"
                                placeholder="Company"
                                value={formData.company}
                                onChange={handleChange}
                            />
                        </div>
                    </div>
                    <div className="flex items-center mb-4">
                        <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                            Private Visitor?
                        </label>
                        <div className="w-3/4">
                            <CustomDropdown
                                value={formData.isPrivate}
                                options={[
                                    { label: "No", value: "No" },
                                    { label: "Yes", value: "Yes" },
                                ]}
                                placeholder="Select Option"
                                onSelect={(value) => setFormData({ ...formData, isPrivate: value })}
                                bgColor="bg-[white] text-black"
                                textColor="text-black"
                                hoverBgColor="hover:bg-[#4F2683]"
                                borderColor="border-gray-300"
                                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                                rounded="rounded"
                            />

                        </div>
                    </div>
                    <div className="flex justify-center gap-4 mt-6">
                        <Button type="cancel" label="Cancel" onClick={onClose} disabled={loading} />
                        <Button type="primary" label={loading ? "Adding..." : "Add"} disabled={loading} />
                    </div>
                </form>
            </div>
        </div>
    );
};

export default AddGuestForm;
