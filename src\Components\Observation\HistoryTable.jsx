import React from "react";
import DataTable from "react-data-table-component";
import userImg from "../../Images/fromimg.svg"
import formatDateTime from "../../utils/formatDateTime";
const customStyles = {
    headCells: {
        style: {
            // width: "100%",
            fontWeight: 500,
            // justifyContent: 'center',
            fontSize: "14px",
            color: "#9971CB",
        },
    },
    rows: {
        style: {
            borderBottom: "1px solid #e0e0e0",
        },
    },
};

const columns = [
    {
        name: "Effective Date",
        selector: row => row.effective_date ? formatDateTime(row.effective_date) : ''  ,   
        sortable: true,
        width: "18%"
    },
    {
        name: "Changed By",
        selector: row => row.updated_by,
        cell: row => (
            <div className="flex items-center justify-center" >
                {/* <img src={row.avatar} alt="avatar" style={{ width: 30, height: 30, borderRadius: "50%", marginRight: 10 }} /> */}
                <span>{row.updated_by}</span>
            </div>
        ),
        width: "15%"
        // center: true,
    },
    {
        name: "Event Type",
        selector: row => row.event_type
        ,
        cell: row => {
            console.log("Event Type cell row:", row);
            return <span className="bg-[#f4f2f7] rounded-xl px-5 py-1 text-[#4F2683]">{row.event_type
            }</span>;
        },
        width: "15%",
        center: true,
    },
    {
        name: "Field Changed",
        selector: row => row.field_changes
        ,
        width: "32%",
    },
    {
        name: "From",
        selector: row => row.old_value,
        center: true,
        width: "10%"
    },
    {
        name: "To",
        selector: row => row.new_value,
        center: true,
        width: "10%"
    },
];

const data = [
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Target annual bonus percent", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
    { effectiveDate: "05-07-2024", changedBy: "Jon Smit", avatar: userImg, eventType: "Hire", fieldChanged: "Base compensation", from: "********", to: "****" },
];
const HistoryTable = ({ isOpen, onClose, data }) => {
    console.log("HistoryTable data", data);
    const handleOverlayClick = (e) => {
        // Close the filter panel if the overlay is clicked
        if (e.target === e.currentTarget) {
            onClose();
        }
    };
    return (
        <div
            className={`fixed top-0 left-0 h-full w-full bg-gray-900 bg-opacity-50 z-40 ${isOpen ? "block" : "hidden"}`}
            onClick={handleOverlayClick}>
            <div
                className={`fixed top-0 right-0 h-full w-[80%] p-2 bg-white shadow-lg rounded-l-md transform transition-transform duration-300 z-50 ${isOpen ? "translate-x-0" : "translate-x-full"}`}
            >
                <div className="flex justify-between border-b-2 pb-4 items-center pt-2 ">
                    <h1 className=" pl-2 font-normal text-[23px] text-[#4F2683]">View History</h1>
                    <button
                        className=" rounded-full text-2xl text-white w-8 h-8 flex items-center justify-center bg-[#4F2683]"
                        onClick={onClose}
                    >&times;</button>
                </div>
                <DataTable
                    columns={columns}
                    data={data || []}
                    customStyles={customStyles}
                    fixedHeader
                    fixedHeaderScrollHeight="700px"
                />
            </div>
        </div>
    )
}
export default HistoryTable;