import React from "react";
import GenericTable from "../../Components/GenericTable"; // Import GenericTable
import DetailsCard from "../../Components/Global/DetailsCard"; // Import DetailsCard
import userImg from "../../Images/guest-image2.png"; // Import default user image

const ViewGuestHistory = ({ guest, onClose }) => {
  // const historyData = [
  //   {
  //     guestId: "352672",
  //     category: "Scheduled",
  //     host: "<PERSON>, 234...",
  //     checkInTime: "19-Mar-2025 | 11:46 PM",
  //     checkOutTime: "19-Mar-2025 | 11:46 PM",
  //     status: "Check-in",
  //   },
  //   {
  //     guestId: "752671",
  //     category: "Walk In",
  //     host: "<PERSON>, 324...",
  //     checkInTime: "19-Mar-2025 | 11:46 PM",
  //     checkOutTime: "19-Mar-2025 | 11:46 PM",
  //     status: "Check-out",
  //   },
  //   // ...other history records...
  // ];

  const columns = [
    { name: "Guest ID", selector: (row) => row.guestId },
    { name: "Category", selector: (row) => row.category },
    { name: "Host", selector: (row) => row.host },
    { name: "Check In Time", selector: (row) => row.checkInTime },
    { name: "Check Out Time", selector: (row) => row.checkOutTime },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${
            row.status.toLowerCase() === "check-in"
              ? "bg-[#107C0F1A] bg-opacity-10 text-[#00BA00]"
              : "bg-[#E21B1B14] bg-opacity-8 text-[#D15858]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-5xl bg-white rounded-lg shadow-lg">
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            View Check In History
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <div className="p-6">
          <DetailsCard
            OpenPhotoModal={() => { }}
            profileImage={guest.profileImage || userImg}
            defaultImage={userImg}
            name={guest.name}
            additionalFields={[
              { label: "Company", value: guest.eid || "N/A" },
              { label: "Gmail", value: guest.company || "N/A" },
              { label: "Is Private?", value: guest.email || "N/A" },
            ]}
          />

          

          <GenericTable
            title="Guest History"
            columns={columns}
            // data={historyData}
            showAddButton={false} // No add button needed
          />
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ViewGuestHistory;
