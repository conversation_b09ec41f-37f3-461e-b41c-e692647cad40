import { useState, useCallback, useEffect } from "react";
import { getBuildingsByFacility } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for building data keyed by facilityId
let buildingMasterDataCache = {};

export const useBuildingData = (facilityId) => {
  const [buildings, setBuildings] = useState(
    facilityId && buildingMasterDataCache[facilityId]
      ? buildingMasterDataCache[facilityId]
      : []
  );

  const fetchBuildings = useCallback(async () => {
    if (!facilityId) return;
    try {
      const response = await getBuildingsByFacility(facilityId);
      const buildingArray = response.data?.data || [];
      const fetchedBuildings = buildingArray.map((b) => ({
        label: b.name,
        value: b.building_id,
        code: b.building_code,
      }));
      buildingMasterDataCache[facilityId] = fetchedBuildings;
      setBuildings(fetchedBuildings);
    } catch (error) {
      console.error("Error fetching buildings:", error);
      toast.error("Error fetching buildings");
    }
  }, [facilityId]);

  useEffect(() => {
    fetchBuildings();
  }, [facilityId, fetchBuildings]);

  return buildings;
};
