import React, { useState } from "react";
import Button from "../Global/Button";

function AssignedIdentitiesAdd({ onSubmit, onClose, availableIdentities }) {
  // Default sample assigned identities (6–8 entries)
  const defaultIdentities = [
    {
      name: "<PERSON>",
      eid: "E001",
      type: "Employee",
      company: "Company A",
      organization: "Org1",
      jobTitle: "Developer",
      endDate: "2023-12-31",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E002",
      type: "Manager",
      company: "Company B",
      organization: "Org2",
      jobTitle: "Team Lead",
      endDate: "2023-11-30",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E003",
      type: "Employee",
      company: "Company C",
      organization: "Org3",
      jobTitle: "Designer",
      endDate: "2023-10-15",
      status: "Inactive",
    },
    {
      name: "<PERSON>",
      eid: "E004",
      type: "Director",
      company: "Company D",
      organization: "Org4",
      jobTitle: "Project Manager",
      endDate: "2023-09-30",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E005",
      type: "Employee",
      company: "Company E",
      organization: "Org5",
      jobTitle: "QA Engineer",
      endDate: "2023-08-31",
      status: "Active",
    },
    {
      name: "Fiona Gallagher",
      eid: "E006",
      type: "Manager",
      company: "Company F",
      organization: "Org6",
      jobTitle: "HR Manager",
      endDate: "2023-07-31",
      status: "Inactive",
    },
    {
      name: "George Clooney",
      eid: "E007",
      type: "Employee",
      company: "Company G",
      organization: "Org7",
      jobTitle: "Support",
      endDate: "2023-06-30",
      status: "Active",
    },
    {
      name: "Hannah Montana",
      eid: "E008",
      type: "Employee",
      company: "Company H",
      organization: "Org8",
      jobTitle: "Sales",
      endDate: "2023-05-31",
      status: "Active",
    },
  ];

  // Use provided availableIdentities if available, else defaultIdentities
  const identitiesList =
    availableIdentities && availableIdentities.length
      ? availableIdentities
      : defaultIdentities;

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedIdentity, setSelectedIdentity] = useState(null);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);

  // Filter identities based on search term (checks name, type, and eid)
  const filteredIdentities = identitiesList.filter((identity) => {
    const term = searchTerm.toLowerCase();
    return (
      identity.name.toLowerCase().includes(term) ||
      identity.type.toLowerCase().includes(term) ||
      identity.eid.toLowerCase().includes(term)
    );
  });

  const handleSelectIdentity = (identity) => {
    setSelectedIdentity(identity);
    setSearchTerm(`${identity.name} - ${identity.type} - ${identity.eid}`);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedIdentity) {
      alert("Please select an identity.");
      return;
    }
    onSubmit(selectedIdentity);
    onClose();
  };

  return (
    <div className="w-full p-4">
      {/* Header */}
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Add Assigned
        </h2>
        <button
          className="w-8 h-8 bg-[#4F2683] flex justify-center items-center p-0 text-white text-2xl rounded-full"
          onClick={onClose}
          type="button"
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
        {/* Identity Search and Dropdown */}
        <div className="mb-4 flex items-center">
          <label className="text-[16px] font-normal w-1/4">
            Select Identity
          </label>
          <div className="relative w-3/4">
            <input
              type="text"
              placeholder="Search Identity"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownVisible(true);
              }}
              onFocus={() => setIsDropdownVisible(true)}
              onBlur={() =>
                setTimeout(() => setIsDropdownVisible(false), 150)
              }
              className="w-full h-11 border border-gray-300 rounded px-3"
            />
            {isDropdownVisible && (
              <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                {filteredIdentities.length > 0 ? (
                  filteredIdentities.map((identity) => (
                    <div
                      key={identity.eid}
                      className="p-2 cursor-pointer hover:bg-gray-100"
                      onMouseDown={() => handleSelectIdentity(identity)}
                    >
                      {identity.name} - {identity.type} - {identity.eid}
                    </div>
                  ))
                ) : (
                  <div className="p-2 text-gray-700 text-center">
                    No Results Found.
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <Button
            type="button"
            label="Cancel"
            onClick={onClose}
            className="bg-gray-400 text-white"
          />
          <Button
            type="submit"
            label="Add"
            className="bg-[#4F2683] text-white"
          />
        </div>
      </form>
    </div>
  );
}

export default AssignedIdentitiesAdd;
