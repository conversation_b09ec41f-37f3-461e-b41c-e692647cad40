import api from "./";

/**
 * Create a new event.
 *
 * @param {object} eventData - The data for the new event.
 * @returns {Promise<any>} A promise that resolves to the created event data.
 */
export const createEvent = async (eventData) => {
  const response = await api.post("/visits/create-event", eventData);
  return response.data;
};

/**
 * Get all events with pagination and filtering.
 *
 * @param {object} params - Query parameters for filtering and pagination.
 * @returns {Promise<any>} A promise that resolves to the list of events.
 */ 
export const getEvents = async (params = {}) => {
  const query = {};
  if (params.search) query.search = params.search; // Add search functionality
  if (params.sortBy) query.sortBy = params.sortBy; // Add sorting functionality
  if (params.sortOrder) query.sortOrder = params.sortOrder; // Add sorting order
  if (params.page) query.page = params.page;
  if (params.limit) query.limit = params.limit;

  const response = await api.get("guests/visits", { params: query });
  return response.data;
};

/**
 * Get an event by ID.
 *
 * @param {string} eventId - The ID of the event to retrieve.
 * @returns {Promise<any>} A promise that resolves to the event data.
 */
export const getEventById = async (eventId) => {
  const response = await api.get(`/guests/visits/${eventId}`);
  return response.data;
};

/**
 * Update an event by ID.
 *
 * @param {string} eventId - The ID of the event to update.
 * @param {object} eventData - The data to update the event.
 * @returns {Promise<any>} A promise that resolves to the updated event data.
 */
export const updateEvent = async (eventId, eventData) => {
  const response = await api.put(`/guests/visits/${eventId}`, eventData);
  return response.data;
};

/**
 * Delete an event by ID.
 *
 * @param {string} eventId - The ID of the event to delete.
 * @returns {Promise<any>} A promise that resolves to the deletion result.
 */
export const deleteEvent = async (eventId) => {
  const response = await api.delete(`/guests/visits/${eventId}`);
  return response.data;
};
