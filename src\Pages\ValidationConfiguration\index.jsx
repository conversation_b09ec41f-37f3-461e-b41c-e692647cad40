import React, { useState } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import { IoFilter } from "react-icons/io5";
import FilterPanel from "../../Components/Observation/FilterPanel";
import newWindow from "../../Images/new-window.svg";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import { ConfigurationData } from "../../api/static";
import AddConfigurationForm from "../../Components/ValidationTask/AddForm";
import ValidationViewEditConfiguration from "../../Components/ValidationTask/ValidationViewEditConfiguration";
import { FaEdit } from "react-icons/fa";
import Delete from "../../Images/Delete.svg";
function ValidationConfiguration() {
  const [tableData, setTableData] = useState(ConfigurationData);
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showViewEditModal, setShowViewEditModal] = useState(false);
  const [selectedConfiguration, setSelectedConfiguration] = useState(null);
  const [startInEditMode, setStartInEditMode] = useState(false);

  // Callback to add new configuration at the top of the table data
  const handleAddConfiguration = (newConfiguration) => {
    setTableData((prevData) => [newConfiguration, ...prevData]);
  };

  // Callback to update a configuration after view/edit
  const handleUpdateConfiguration = (updatedConfig) => {
    setTableData((prevData) =>
      prevData.map((config) =>
        config.id === updatedConfig.id ? updatedConfig : config
      )
    );
  };

  // Delete configuration by filtering it out based on id
  const handleDeleteConfiguration = (id) => {
    setTableData((prevData) => prevData.filter((config) => config.id !== id));
  };

  // Filter the table data based on search term and selected filter
  const filteredData = tableData
    .filter((row) => (filter === "All" ? true : row.status === filter))
    .filter(
      (row) =>
        row.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (row.system &&
          row.system.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (row.type && row.type.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (row.creation &&
          row.creation.toLowerCase().includes(searchTerm.toLowerCase()))
    );

  // Define table columns (including a new Actions column)
  const columns = [
    {
      name: "Title",
      selector: (row) => row.title,
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          // Clicking the title opens the modal in view mode
          onClick={() => {
            setSelectedConfiguration(row);
            setStartInEditMode(false);
            setShowViewEditModal(true);
          }}
        >
          <span>{row.title}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt="new window" />
          )}
        </div>
      ),
    },
    {
      name: <TruncatedCell text="Description" />,
      selector: (row) => row.description,
    },
    {
      name: <TruncatedCell text="Trigger type" />,
      selector: (row) => row.type,
    },
    {
      name: <TruncatedCell text="Owner" />,
      selector: (row) => row.owner,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full `}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <>
          <FaEdit
            size={32}
            className="rounded-lg cursor-pointer text-green-500 items-center m-1 p-2 bg-[#F0EDF5]"
            onClick={(e) => {
              e.stopPropagation();
              setSelectedConfiguration(row);
              setStartInEditMode(true);
              setShowViewEditModal(true);
            }}
          />
          <img src={Delete} alt="Delete" className="cursor-pointer p-2 rounded-lg m-1 bg-[#E21B1B14]"  onClick={(e) => {
              e.stopPropagation();
              handleDeleteConfiguration(row.id);
            }}/>
        </>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <>
      <div className="flex flex-col px-8 py-4 pl-20 pt-20">
        <div className="mb-6">
          <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">
            Validation Configuration
          </h2>
        </div>
        <div className="mb-4 flex items-center space-x-4">
          <FilterButtons
            filter={filter}
            onFilterChange={setFilter}
            filterOptions={[
              { label: "All", value: "All" },
              { label: "Active", value: "Active" },
            ]}
          />
        </div>
        <GenericTable
          title="Validation Configuration"
          searchTerm={searchTerm}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          columns={columns}
          // Trigger the Add modal
          onAdd={() => setShowAddModal(true)}
          extraControls={
            <IoFilter
              className="bg-white shadow-sm border p-1 text-[#4F2683] h-8 w-8 rounded cursor-pointer"
              onClick={() => setIsFilterPanelOpen(true)}
            />
          }
          data={filteredData}
          fixedHeader
          fixedHeaderScrollHeight="440px"
        />
        <FilterPanel
          isOpen={isFilterPanelOpen}
          onClose={() => setIsFilterPanelOpen(false)}
        />
      </div>

      {/* Add Configuration Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddConfigurationForm
                onClose={() => setShowAddModal(false)}
                onAddConfiguration={handleAddConfiguration}
              />
            </div>
          </div>
        </div>
      )}

      {/* View/Edit Configuration Modal */}
      {showViewEditModal && selectedConfiguration && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ValidationViewEditConfiguration
                configurationData={selectedConfiguration}
                onUpdate={(updatedConfig) => {
                  handleUpdateConfiguration(updatedConfig);
                  setShowViewEditModal(false);
                  setSelectedConfiguration(null);
                  setStartInEditMode(false);
                }}
                onClose={() => {
                  setShowViewEditModal(false);
                  setSelectedConfiguration(null);
                  setStartInEditMode(false);
                }}
                startInEditMode={startInEditMode}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default ValidationConfiguration;
