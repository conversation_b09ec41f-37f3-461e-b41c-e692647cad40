import React, { useState } from "react";
import Button from "../Global/Button";
import CustomDropdown from "../Global/CustomDropdown";

function ApproversAdd({ onSubmit, onClose, availableApprovers }) {
  const defaultApprovers = [
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
    "<PERSON>",
  ];

  // Use provided availableApprovers if available, else defaultApprovers
  const approversData =
    availableApprovers && availableApprovers.length
      ? availableApprovers
      : defaultApprovers;

  // Determine if approversData is an array of objects
  const isObjectApprovers =
    Array.isArray(approversData) && typeof approversData[0] === "object";

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedApprover, setSelectedApprover] = useState("");
  const [selectedLevel, setSelectedLevel] = useState("Level 1");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);

  // Filter approvers based on search term.
  // If objects, search in name, type, and eid (if available); otherwise, search in the string.
  const filteredApprovers = isObjectApprovers
    ? approversData.filter((approver) => {
        const term = searchTerm.toLowerCase();
        return (
          approver.name.toLowerCase().includes(term) ||
          (approver.type && approver.type.toLowerCase().includes(term)) ||
          (approver.eid && approver.eid.toLowerCase().includes(term))
        );
      })
    : approversData.filter((name) =>
        name.toLowerCase().includes(searchTerm.toLowerCase())
      );

  const handleSelectApprover = (approver) => {
    if (isObjectApprovers) {
      setSelectedApprover(approver);
      setSearchTerm(
        `${approver.name}${
          approver.type ? " - " + approver.type : ""
        }${approver.eid ? " - " + approver.eid : ""}`
      );
    } else {
      setSelectedApprover(approver);
      setSearchTerm(approver);
    }
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedApprover) {
      alert("Please select an approver.");
      return;
    }
    let selectedApproverObj;
    if (isObjectApprovers) {
      selectedApproverObj = { ...selectedApprover, approverLevel: selectedLevel };
    } else {
      selectedApproverObj = { name: selectedApprover, approverLevel: selectedLevel };
    }
    onSubmit(selectedApproverObj);
    onClose();
  };

  return (
    <div className="w-full p-0">
      {/* Header */}
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Add Approvers</h2>
        <button
          className="w-8 h-8 bg-[#4F2683] flex justify-center items-center p-0 text-white text-2xl rounded-full"
          onClick={onClose}
          type="button"
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />

      {/* Form */}
      <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
        {/* Approver Selection with Search & Dropdown */}
        <div className="mb-4 flex items-center">
          <label className="text-[16px] font-normal w-1/4">Select Approver</label>
          <div className="relative w-3/4">
            <input
              type="text"
              placeholder="Search Approver"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value);
                setIsDropdownVisible(true);
              }}
              onFocus={() => setIsDropdownVisible(true)}
              onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
              className="w-full h-11 border border-gray-300 rounded px-3"
            />
            {isDropdownVisible && (
              <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                {filteredApprovers.length > 0 ? (
                  filteredApprovers.map((approver, index) => (
                    <div
                      key={
                        isObjectApprovers
                          ? approver.eid || index
                          : approver
                      }
                      className="p-2 cursor-pointer hover:bg-gray-100"
                      onMouseDown={() => handleSelectApprover(approver)}
                    >
                      {isObjectApprovers
                        ? `${approver.name}${
                            approver.type ? " - " + approver.type : ""
                          }${approver.eid ? " - " + approver.eid : ""}`
                        : approver}
                    </div>
                  ))
                ) : (
                  <div className="p-2 text-gray-700 text-center">
                    No Results Found.
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Approver Level Dropdown */}
        <div className="mb-4 flex items-center">
          <label className="text-[16px] font-normal w-1/4">
            Select Approver Level
          </label>
          <div className="w-3/4">
            <CustomDropdown
              options={["Level 1", "Level 2", "Level 3"]}
              defaultValue="Level 1"
              value={selectedLevel}
              onSelect={(option) => setSelectedLevel(option)}
              placeholder="Select Level"
              bgColor="bg-white"
              textColor="text-black"
              borderColor="border-gray-300"
              rounded="rounded"
              className="w-full h-11"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4 justify-center">
          <Button
            type="button"
            label="Cancel"
            onClick={onClose}
            className="bg-gray-400 text-white"
          />
          <Button
            type="submit"
            label="Add"
            className="bg-[#4F2683] text-white"
          />
        </div>
      </form>
    </div>
  );
}

export default ApproversAdd;
