import React, { useState, useEffect } from 'react';
import EditableSection from '../Global/EditableSection';
import { getPatientInformation, updatePatientDetails, updateAdmissionDetails, updateFacilityDetails, updatePatientAddress } from '../../api/PatientHub';
import { toast, ToastContainer } from 'react-toastify'; // Import toast and ToastContainer
import 'react-toastify/dist/ReactToastify.css'; // Import toast styles
import { useCountryMasterData } from '../../hooks/useCountryMasterData';
import { useStateMasterData } from '../../hooks/useStateMasterData';
import  formatDateTime from '../../utils/formatDate'; // Assuming you have a utility function for date formatting
const Demographic = ({ patientId }) => {
  const [biographicData, setBiographicData] = useState({
    FirstName: '',
    MiddleName: '',
    LastName: '',
   Alias: '',
    PreferredName: '', 
    Email: '',
    Phone: '',
    DateOfBirth: '', 
  });

const { countries: countryData = [] } = useCountryMasterData();
const [selectedCountry, setSelectedCountry] = useState(null);
const states = useStateMasterData(selectedCountry);


const countryOptions = countryData.map(c => ({
  label: c.name,
  value: c.country_id
}));

const stateOptions = Array.isArray(states)
  ? states.map(s => ({ label: s.name, value: s.state_id }))
  : [];

  const [admissionData, setAdmissionData] = useState({
    PatientType: '',
    AdmissionDate: '',
    DischargeDate: '-',
    PatientDeceased: '-',
    DateTimeofDeceased: '',
    Confidential: '-',
  });

  const [facilityData, setFacilityData] = useState({
    Facility: '',
    Building: '',
    Floor: '',
    Room: '',
    BedNumber: '',
  });

  const [addressData, setAddressData] = useState({
    Address1: '',
    Address2: '',
    country_id: '',
    state_id: '',
    City: '',
    Zip: '',
  });

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        if (!patientId) return;

        const response = await getPatientInformation({ patient_id: patientId });

        setBiographicData({
          FirstName: response.first_name || '',
          MiddleName: response.middle_name || '',
          LastName: response.last_name || '',
          PreferredName: response.preferred_name || '',
          Alias: response.alias || '',
          Email: response.email || '',
          Phone: response.phone || '',
        DateOfBirth:
          response.birth_date
            ? formatDateTime(response.birth_date)
            : '',

        });

       setAdmissionData({
          PatientType: response.type || '',
          AdmissionDate: response.arrival_time
            ? formatDateTime(response.arrival_time)
            : '',
          DischargeDate: response.discharge_time || '-',
          PatientDeceased: response.death_date  ? formatDateTime(response.death_date )
            : '',
          DateTimeofDeceased: response.datetime_of_deceased || '',
          Confidential: response.confidentiality_code || '-',
        });

        setFacilityData({
          Facility: response.facility || '',
          Building: response.building || '',
          Floor: response.floor || '',
          Room: response.room || '',
          BedNumber: response.beds || '',
        });

        setAddressData({
          Address1: response.address_line_1 || '',
          Address2: response.address_line_2 || '',
         country_id: '',   // <-- must match dropdownKeys
  state_id: '', 
          City: response.city || '',
          Zip: response.postal_code || '',
        });
      } catch (error) {
        console.error('API Error:', error);
        toast.error("Failed to fetch patient data.", { // Add toast for fetch error
          position: "top-right",
          autoClose: 3000,
          hideProgressBar: true,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
          theme: "light",
        });
      }
    };

    fetchPatientData();
  }, [patientId]);

  const handleChange = (section, key, value) => {
    // console.log("🚀 ~ handleChange ~ section, key, value:", section, key, value)

    switch (section) {
      case 'biographicData':
        setBiographicData((prev) => ({ ...prev, [key]: value }));
        // console.log("🚀 ~ handleChange ~ biographicData:", biographicData)
        break;
      case 'admissionData':
        setAdmissionData((prev) => ({ ...prev, [key]: value }));
        break;
      case 'facilityData':
        setFacilityData((prev) => ({ ...prev, [key]: value }));
        break;
      case 'addressData':
        setAddressData((prev) => ({ ...prev, [key]: value }));
        break;
      default:
        break;
    }
  };

  const mapPayload = (section, data) => {
    switch (section) {
      case 'biographicData':
        return {
          first_name: data.FirstName,
          middle_name: data.MiddleName,
          last_name: data.LastName,
          preferred_name: data.PreferredName,
          email: data.Email,
          phone: data.Phone,
           birth_date:   data.RawBirthDate,
        };
      case 'admissionData':
        // Helper function to parse date safely
        const parseDate = (dateString) => {
          if (!dateString || dateString === '-') return null;
          const date = new Date(dateString);
          return isNaN(date.getTime()) ? null : date.toISOString();
        };

        return {
          type: parseInt(data.PatientType, 10) || 0,
          discharge_time: parseDate(data.DischargeDate),
          death_date: data.PatientDeceased && data.PatientDeceased !== '-'
            ? new Date(data.PatientDeceased).toISOString().split('T')[0]
            : null,
          confidentiality_code: parseInt(data.Confidential, 10) || 0,
        };
      case 'facilityData':
        return {
          facility_id: data.Facility || '',
          facility_name: data.FacilityName || '',
          building_id: data.Building || '',
          building_name: data.BuildingName || '',
          floor_id: data.Floor || '',
          floor_number: parseInt(data.FloorNumber, 10) || 0,
          room_id: data.Room || '',
          room_number: data.RoomNumber || '',
          beds: parseInt(data.BedNumber, 10) || 0,
        };
      case 'addressData':
        return {
          address_line_1: data.Address1,
          address_line_2: data.Address2,
          country: data.Country,
          state: data.State,
          city: data.City,
          postal_code: data.Zip,
        };
      default:
        return data;
    }
  };



  const handleSave = async (section, data) => {

    try {
      console.log('Saving data for section:', section);

      const mappedData = mapPayload(section, data);

      let response;
      switch (section) {
        case 'biographicData':
          response = await updatePatientDetails(patientId, mappedData);
          break;
        case 'admissionData':
          response = await updateAdmissionDetails(patientId, mappedData);
          break;
        case 'facilityData':
          response = await updateFacilityDetails(patientId, mappedData);
          break;
        case 'addressData':
          response = await updatePatientAddress(patientId, mappedData);
          break;
        default:
          break;
      }

      console.log('API Response:', JSON.stringify(response, null, 2));
      toast.success('Data updated successfully!', { // Success toast
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    } catch (error) {
      console.error('Error updating data:', error);
      const errorMessage = error?.response?.data?.message || 'Failed to update data. Please try again.';
      toast.error(errorMessage, { // Error toast
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    }
  };
  console.log("🚀 ~ Demographic ~ biographicData:", biographicData)

  return (
    <div className="bg-gray-100 min-h-screen">
      <EditableSection
        title="Patient Info"
        data={biographicData}
        onChange={(key, value) => handleChange('biographicData', key, value)}
        // onChange={handleChange}
dateKeys={['DateOfBirth']}
        onSave={(data) => handleSave("biographicData", data)}
      />
      <EditableSection
        title="Admission Info"
        data={admissionData}
        onChange={(key, value) => handleChange('admissionData', key, value)}
        onSave={(data) => handleSave('admissionData', data)}
        editableKeys={[]} // Make all fields non-editable
        hideEditIcon={true} // Hide the edit icon
      />
      <EditableSection
        title="Facility"
        data={facilityData}
        onChange={(key, value) => handleChange('facilityData', key, value)}
        onSave={(data) => handleSave('facilityData', data)}
        editableKeys={[]} // Make all fields non-editable
        hideEditIcon={true} // Hide the edit icon
      />
      <EditableSection
  title="Address"
  data={addressData}
  onChange={(key, val) => {
    handleChange('addressData', key, val);
    if (key === 'country_id') {
      setSelectedCountry(val);           // refresh states
      handleChange('addressData', 'state_id', ''); // clear old state
    } 
  }}
  onSave={(d) => handleSave('addressData', d)}
  
  // tell it which fields should become dropdowns:
  dropdownKeys={['country_id', 'state_id']}
  
  // pass the option arrays it should use:
  dropdownOptions={{
    country_id: countryOptions,
    state_id: stateOptions
  }}
  
  // if you want the search‐box inside the country dropdown:
  searchableKeys={['country_id']}
  
  // optional: hide the little pencil if you want
  hideEditIcon={false}
/>

      <ToastContainer /> {/* Add ToastContainer here */}
    </div>
  );
};

export default Demographic;