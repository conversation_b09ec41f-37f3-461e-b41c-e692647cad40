import React, { useState, useRef, useEffect } from "react";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Input from "../../Global/Input/Input";
import Button from "../../Global/Button";
import SearchableVisitor from "../../Global/SearchableVisitors";
import AddNewGuest from "./AddNewGuest";
import { SearchableVisitors as staticVisitors } from "../../../api/static";
import DateTimeInput from "../../Temporary/DateTimeInput";
import CustomDropdown from "../../Global/CustomDropdown";
import { getMasterData, getFacilities, searchIdentities, getAccessLevels } from "../../../api/global";
import { createEvent } from "../../../api/event"; 
// Validation schema (adjust as needed)
const validationSchema = Yup.object().shape({
  visitTitle: Yup.string().required("Visit Title is required"),
  visitType: Yup.string().required("Visit Type is required"),
  startDateTime: Yup.string().required("Start Date & Time is required"),
  endDateTime: Yup.string().required("End Date & Time is required"),
  visitorFacility: Yup.string().required("Visitor Facility is required"),
  host: Yup.string().required("Host is required"),
  checkInInstructions: Yup.string().required("Check In Instruction is required"),
});

const AddVisitForm = ({ visible, onClose, onSave }) => {
  const [isGuestAddOn, setIsGuestAddOn] = useState(false);
  const [visitors, setVisitors] = useState(staticVisitors);
  const [eventTypeOptions, setEventTypeOptions] = useState([]);
  const [repeatVisitOptions, setRepeatVisitOptions] = useState([]);
  const [eventAccessOptions, setEventAccessOptions] = useState([]);
  const [checkInInstructionsOptions, setCheckInInstructionsOptions] = useState([]);
  const [remindMeOptions, setRemindMeOptions] = useState([]);
  const [facilities, setFacilities] = useState([]);
  const formikRef = useRef();


  useEffect(() => {
    const load = async () => {
      console.log("→ calling getFacilities…", { attributes: ["id", "name"], page: 1, limit: 100 });
      try {
        const resp = await getFacilities({ attributes: ["id", "name"], page: 1, limit: 100 });
        console.log("← getFacilities resolved:", resp);
        // adapt to whatever shape you actually see in resp
        const items = resp.data?.data || [];
        console.log("Parsed facilities:", items);
        setFacilities(items);
      } catch (err) {
        console.error("⚠️ getFacilities threw:", err);
      }

    };
    load();
  }, []);

  useEffect(() => {
    // Fetch access levels for visitorAccess dropdown
    const fetchAccessLevels = async () => {
      try {
        const resp = await getAccessLevels();
        // adapt to whatever shape you actually see in resp
        const items = resp.data?.data || resp.data || [];
        setEventAccessOptions(
          (Array.isArray(items) ? items : []).map(item => ({
            label: item.name,
            value: item.id,
          }))
        );
      } catch (err) {
        console.error("⚠️ getAccessLevels threw:", err);
      }
    };
    fetchAccessLevels();
  }, []);

  useEffect(() => {
    // Fetch master data for dropdowns
    const fetchMasterData = async () => {
      try {
        const resp = await getMasterData({
          groups: ["visit_type", "repeat_visit", "check_in_instruction", "remind_me"],
        });
        console.log("← getMasterData raw response:", resp);

        // most APIs wrap payload under resp.data.data
        const master = resp.data?.data || resp.data || {};
        console.log("Parsed master-data:", master);

        setEventTypeOptions((master.visit_type || []).map(item => ({
          label: item.value,
          value: item.value,
        })));
        setRepeatVisitOptions((master.repeat_visit || []).map(item => ({
          label: item.value,
          value: item.value,
        })));
        setCheckInInstructionsOptions((master.check_in_instruction || []).map(item => ({
          label: item.value,
          value: item.value,
        })));
        setRemindMeOptions((master.remind_me || []).map(item => ({
          label: item.value,
          value: item.value,
        })));
      } catch (error) {
        console.error("Failed to fetch master data:", error);
      }
    };

    fetchMasterData();
  }, []);

  if (!visible) return null;

  // Default form values
  const initialValues = {
    EventTitle: "",
    EventType: "",
    searchVisitors: [],
    startDateTime: null,
    endDateTime: null,
    repeatVisit: "",
    visitorFacility: "",
    visitorAccess: "",
    host: "",
    checkInInstructions: "",
    escort: "",
    sendNotificationsTo: "",
    remindMe: "15 Min Before",
    messageToVisitor: "",
  };

  const handleSubmit = async (values) => {
    console.log("submitting form values:", values);
    try {
      const created = await createEvent(values);
      onSave(created);
    } catch (err) {
      console.error("⚠️ createEvent failed:", err);
      // you might want to show a toast/error message here
    }
  };

  // Callback to add new guest from the modal form
  const handleAddGuest = (newGuest) => {
    newGuest.id = Date.now(); // Ensure a unique id is assigned
    setVisitors((prev) => [...prev, newGuest]);
    // Automatically add the new guest to the selected list
    if (formikRef.current) {
      const currentSelected = formikRef.current.values.searchVisitors;
      formikRef.current.setFieldValue("searchVisitors", [
        ...currentSelected,
        newGuest,
      ]);
    }
    setIsGuestAddOn(false);
  };

  // Async search dropdown for identities
  const AsyncSearchDropdown = ({ value, onChange, placeholder, name }) => {
    const [options, setOptions] = useState([]);
    const [search, setSearch] = useState("");
    const [loading, setLoading] = useState(false);

    // Show selected value in input if not typing
    useEffect(() => {
      if (!search && value && typeof value === "string") {
        setSearch(value);
      }
    }, [value]);

    useEffect(() => {
      if (!search) {
        setOptions([]);
        return;
      }
      let active = true;
      setLoading(true);
      searchIdentities(search).then((data) => {
        if (active) {
          let items = data?.data?.data;
          if (!Array.isArray(items)) {
            if (items && typeof items === "object") {
              items = Object.values(items);
            } else {
              items = [];
            }
          }
          setOptions(items.map((item) => ({
            label: item.name || item.eid || item.email,
            value: item.id || item.eid || item.email,
            ...item,
          })));
          setLoading(false);
        }
      });
      return () => { active = false; };
    }, [search]);

    return (
      <div className="relative">
        <input
          className="p-2 border border-gray-300 rounded w-full focus:outline-none focus:ring-1"
          placeholder={placeholder}
          value={search}
          onChange={e => setSearch(e.target.value)}
          autoComplete="off"
        />
        {loading && <div className="text-xs text-gray-400">Searching...</div>}
        {options.length > 0 && (
          <ul className="border rounded bg-white mt-1 max-h-40 overflow-y-auto z-10 absolute w-full left-0">
            {options.map(opt => (
              <li
                key={opt.value}
                className="p-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => {
                  onChange(opt);
                  setSearch(opt.label);
                  setOptions([]);
                }}
              >
                {opt.label}
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center z-50 py-6">
      <div className="bg-white w-full max-w-6xl p-6 rounded-lg shadow-lg overflow-y-auto h-auto ">
        <div className="flex items-center mb-6 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">Create Event</h2>
          <button
            className="bg-[#4F2683] text-white w-8 h-8 rounded-full text-2xl flex items-center justify-center"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <Formik
          innerRef={formikRef}
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ values, errors, touched, handleChange, handleBlur, setFieldValue }) => (
            <Form>
              <div className="space-y-4">
                {/* Visit Title */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Event Title *
                  </label>
                  <div className="w-3/4">
                    <Input
                      name="visitTitle"
                      placeholder="e.g. John Doe's Business Visit"
                      value={values.visitTitle}
                      onChange={handleChange}
                    />
                    {touched.visitTitle && errors.visitTitle && (
                      <p className="text-red-500 text-sm mt-1">{errors.visitTitle}</p>
                    )}
                  </div>
                </div>

                {/* Event Type */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Event Type *
                  </label>
                  <div className="w-3/4">
                    <CustomDropdown
                      options={eventTypeOptions}
                      value={values.EventType}
                      onSelect={(val) => setFieldValue("EventType", val)}
                      placeholder="Select Event Type"
                      dropDownclassName="w-full"
                      className="h-10"
                    />
                    {touched.EventType && errors.EventType && (
                      <p className="text-red-500 text-sm mt-1">{errors.EventType}</p>
                    )}
                  </div>
                </div>

                {/* Search & Add Existing Visitors */}
                <div className="flex w-full items-center mb-4">
                  <label className="w-1/3 text-[16px] font-normal text-[#333333]">
                    Search & Add Existing Visitors *
                  </label>
                  <div className="w-full flex justify-between">
                    <div className="w-3/4">
                      <SearchableVisitor
                        name="searchVisitors"
                        placeholder="Search by name or email"
                        visitors={visitors}
                        value={values.searchVisitors}
                        onSelect={(selected) =>
                          formikRef.current.setFieldValue("searchVisitors", selected)
                        }
                      />
                    </div>
                    <button
                      type="button"
                      className="bg-[#4F2386] mr-4 text-white py-2 px-3 rounded-full"
                      onClick={() => setIsGuestAddOn(true)}
                    >
                      Add New Guest
                    </button>
                  </div>
                </div>

                {/* Visit Start Date & Time */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Event Start Date & Time *
                  </label>
                  <div className="w-3/4">
                    <DateTimeInput
                      name="startDateTime"
                      value={values.startDateTime}
                      onChange={(date) => setFieldValue("startDateTime", date)}
                      onBlur={handleBlur}
                    />
                    {touched.startDateTime && errors.startDateTime && (
                      <p className="text-red-500 text-sm mt-1">{errors.startDateTime}</p>
                    )}
                  </div>
                </div>

                {/* Visit End Date & Time */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Event End Date & Time *
                  </label>
                  <div className="w-3/4">
                    <DateTimeInput
                      name="endDateTime"
                      value={values.endDateTime}
                      onChange={(date) => setFieldValue("endDateTime", date)}
                      onBlur={handleBlur}
                    />
                    {touched.endDateTime && errors.endDateTime && (
                      <p className="text-red-500 text-sm mt-1">{errors.endDateTime}</p>
                    )}
                  </div>
                </div>

                {/* Repeat Visit */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Repeat Event
                  </label>
                  <div className="w-3/4">
                    <CustomDropdown
                      options={repeatVisitOptions}
                      value={values.repeatVisit}
                      onSelect={(val) => setFieldValue("repeatVisit", val)}
                      placeholder="Select Repeat Event"
                      dropDownclassName="w-full"
                      className="h-10"
                    />
                  </div>
                </div>

                {/* Visitor Facility / Location */}
                {/* 4. Facility / Location dropdown */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Facility / Location
                  </label>
                  <div className="w-3/4">
                    <CustomDropdown
                      value={values.visitorFacility}
                      options={facilities.map(f => ({ label: f.name, value: f.facility_id }))}
                      placeholder="Select Facility"
                      onSelect={(val) => setFieldValue("visitorFacility", val)}
                      bgColor="bg-[white] text-black"
                      textColor="text-black"
                      hoverBgColor="hover:bg-[#4F2683]"
                      borderColor="border-gray-300"
                      className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                      rounded="rounded"
                    />

                  </div>
                </div>


                {/* Visitor Access */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Events Access
                  </label>
                  <div className="w-3/4">
                    <CustomDropdown
                      options={eventAccessOptions}
                      value={values.visitorAccess}
                      onSelect={(val) => setFieldValue("visitorAccess", val)}
                      placeholder="Select Event Access"
                      dropDownclassName="w-full"
                      className="h-10"
                    />
                  </div>
                </div>

                {/* Host */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Host *
                  </label>
                  <div className="w-3/4">
                    <AsyncSearchDropdown
                      name="host"
                      value={values.host}
                      onChange={opt => setFieldValue("host", opt.value)}
                      placeholder="Search by name or EID"
                    />
                    {touched.host && errors.host && (
                      <p className="text-red-500 text-sm mt-1">{errors.host}</p>
                    )}
                  </div>
                </div>

                {/* Check In Instructions */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Check In Instructions *
                  </label>
                  <div className="w-3/4">
                    <CustomDropdown
                      options={checkInInstructionsOptions}
                      value={values.checkInInstructions}
                      onSelect={(val) => setFieldValue("checkInInstructions", val)}
                      placeholder="Select Check In Instructions"
                      dropDownclassName="w-full"
                      className="h-10"
                    />
                    {touched.checkInInstructions && errors.checkInInstructions && (
                      <p className="text-red-500 text-sm mt-1">{errors.checkInInstructions}</p>
                    )}
                  </div>
                </div>

                {/* Escort */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Escort
                  </label>
                  <div className="w-3/4">
                    <AsyncSearchDropdown
                      name="escort"
                      value={values.escort}
                      onChange={opt => setFieldValue("escort", opt.value)}
                      placeholder="Search by name or EID"
                    />
                  </div>
                </div>

                {/* Send Notifications To */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Send Notifications To
                  </label>
                  <div className="w-3/4">
                    <AsyncSearchDropdown
                      name="sendNotificationsTo"
                      value={values.sendNotificationsTo}
                      onChange={opt => setFieldValue("sendNotificationsTo", opt.value)}
                      placeholder="Search by name or EID"
                    />
                  </div>
                </div>

                {/* Remind Me */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Remind Me
                  </label>
                  <div className="w-3/4">
                    <CustomDropdown
                      options={remindMeOptions}
                      value={values.remindMe}
                      onSelect={(val) => setFieldValue("remindMe", val)}
                      placeholder="Select Reminder Time"
                      dropDownclassName="w-full"
                      className="h-10"
                    />
                  </div>
                </div>

                {/* Message To Visitor */}
                <div className="flex items-center mb-4">
                  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
                    Message To Events
                  </label>
                  <div className="w-3/4">
                    <textarea
                      name="messageToEvents"
                      placeholder="Enter message to be sent with invitation"
                      value={values.messageToVisitor}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      className="p-2 border border-gray-300 rounded w-full focus:outline-none focus:ring-1"
                      rows="3"
                    />
                  </div>
                </div>
              </div>

              {/* Buttons */}
              <div className="flex justify-center gap-4 mt-6">
                <Button type="cancel" label="Cancel" onClick={onClose} />
                <Button type="primary" label="Save" buttonType="submit" />
              </div>
            </Form>
          )}
        </Formik>
      </div>
      {isGuestAddOn && (
        <AddNewGuest
          onClose={() => setIsGuestAddOn(false)}
          onAdd={handleAddGuest}
        />
      )}
    </div>
  );
};

export default AddVisitForm;
