import React, { useState } from "react";
import GenericTable from "../../GenericTable";
import ViewReport from "./ViewReport"; // Import ViewReport modal
import deletedIcon from "../../../Images/Delete.svg"; // Import delete icon
import usersetting from "../../../Images/UserSetting.svg";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';


const ViewAccess = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState("Facility");
  const [showViewReport, setShowViewReport] = useState(false); // State to manage ViewReport modal
  const [areaData, setAreaData] = useState([
    {
      name: "<PERSON>",
      eid: "E123",
      role: "Admin",
      owner: "Yes",
      company: "ABC Corp",
      phone: "1234567890",
      email: "<EMAIL>",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "E124",
      role: "User",
      owner: "No",
      company: "XYZ Ltd",
      phone: "9876543210",
      email: "<EMAIL>",
      status: "Inactive",
    },
  ]);

  const FacilityData = [
    { facilityName: "John Doe", code: "E123", type: "ASDF", city: "ABCD", state: "ABC Corp", country: "India", status: "Active" },
    { facilityName: "Jane Smith", code: "E124", type: "SDFGH", city: "WXYZ", state: "XYZ Ltd", country: "USA", status: "Inactive" },
  ];

  const handleDelete = (eid) => {
    // Function to delete a row based on EID
    setAreaData(areaData.filter((row) => row.eid !== eid));
    toast.error("User deleted successfully!");
  };

  const FacilityColumns = [
    { name: "Facility Name", selector: (row) => row.facilityName, sortable: true },
    { name: "Code", selector: (row) => row.code },
    { name: "Type", selector: (row) => row.type },
    { name: "City", selector: (row) => row.city },
    { name: "State", selector: (row) => row.state },
    { name: "Country", selector: (row) => row.country },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${
            row.status.toLowerCase() === "active"
              ? "bg-[#107C0F1A] bg-opacity-10 text-[#00BA00]"
              : "bg-[#E21B1B14] bg-opacity-8 text-[#D15858]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  const areaColumns = [
    { name: "Name", selector: (row) => row.name, sortable: true },
    { name: "EID", selector: (row) => row.eid },
    { name: "Email", selector: (row) => row.email },
    { name: "Company", selector: (row) => row.company },
    { name: "Role", selector: (row) => row.role },
    { name: "Owner", selector: (row) => row.owner },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${
            row.status.toLowerCase() === "active"
              ? "bg-[#107C0F1A] bg-opacity-10 text-[#00BA00]"
              : "bg-[#E21B1B14] bg-opacity-8 text-[#D15858]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <div className="flex gap-2">
          <img src={usersetting} alt="usersetting" onClick={() => setShowViewReport(true)} className={`bg-[#EEE9F2] px-1 py-1 rounded ${row.status.toLowerCase() === "returned" ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                            }`} />
         <img src={deletedIcon} alt="deletedIcon" onClick={() => handleDelete(row.eid)} className={`bg-[#E21B1B14] px-1 py-1 rounded ${row.status.toLowerCase() === "returned" ? "opacity-50 cursor-not-allowed" : "cursor-pointer"
                            }`} />
        </div>
      ),
    },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-7xl bg-white rounded-lg shadow-lg h-[90vh] overflow-y-auto">
        {/* Header Section */}
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View Area Details</h2>
          <button
            className="w-8 h-8 text-xl bg-[#4F2683] text-white rounded-full flex items-center justify-center"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>

        {/* Fields Section */}
        <div className="px-6 py-4">
  <div className="grid grid-cols-1 gap-4">
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Area ID</p>
      <p className="text-base text-gray-800">9442</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Area Display Name</p>
      <p className="text-base text-gray-800">ZTEST-LENEL-APAC-1</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Type</p>
      <p className="text-base text-gray-800">Online</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Area Type(S)</p>
      <p className="text-base text-gray-800">Restricted</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Card Type(S)</p>
      <p className="text-base text-gray-800">Multitech General</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">PACS System</p>
      <p className="text-base text-gray-800">ZTEST-LENEL-APAC-1</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">PACS ID</p>
      <p className="text-base text-gray-800">3571725</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">PACS Name</p>
      <p className="text-base text-gray-800">LenelAPAC</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Risk Level</p>
      <p className="text-base text-gray-800">[Not Specified]</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Status</p>
      <p className="text-base text-gray-800">Active</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Requestable By Self Service Person</p>
      <p className="text-base text-gray-800">Yes</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Audit Interval</p>
      <p className="text-base text-gray-800">[Not Specified]</p>
    </div>
    <div className="flex items-center">
      <p className="text-sm font-medium text-gray-600 w-1/3">Area Special Instruction</p>
      <p className="text-base text-gray-800">[Not Specified]</p>
    </div>
  </div>
</div>

        {/* Tab Navigation */}
        <div className="flex gap-2 ms-5 mt-4">
          <button
            className={`w-1/5 py-2 rounded-full font-medium ${activeTab === "Facility"
                ? "bg-[#4F2683] text-white"              // Filled (active)
                : "border border-[#4F2683] text-[#4F2683] bg-[#EAE0F6]" // Outlined (inactive)
              }`}
            onClick={() => setActiveTab("Facility")}
          >
            Facility(s)
          </button>
          <button
            className={`w-1/5 py-2 rounded-full font-medium ${activeTab === "Readers"
                ? "bg-[#4F2683] text-white"              // Filled (active)
                : "border border-[#4F2683] text-[#4F2683] bg-[#EAE0F6]" // Outlined (inactive)
              }`}
            onClick={() => setActiveTab("Readers")}
          >
            Area Role
          </button>
        </div>

        {/* Table Content */}
        <div className="p-6">
          {activeTab === "Facility" && (
            <GenericTable
              showAddButton={false}
              columns={FacilityColumns}
              data={FacilityData}
              fixedHeader
              fixedHeaderScrollHeight="400px"
              highlightOnHover
              striped
            />
          )}
          {activeTab === "Readers" && (
            <GenericTable
              showAddButton={false}
              columns={areaColumns}
              data={areaData}
              fixedHeader
              fixedHeaderScrollHeight="400px"
              highlightOnHover
              striped
            />
          )}
        </div>
      </div>
      {showViewReport && (
        <ViewReport onClose={() => setShowViewReport(false)} />
      )}
    </div>
  );
};

export default ViewAccess;

