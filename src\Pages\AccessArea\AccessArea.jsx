import React, { useState } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import { IoFilter } from "react-icons/io5";
import FilterPanel from "../../Components/Observation/FilterPanel";
import newWindow from "../../Images/new-window.svg";
import { AccessAreaData } from "../../api/static";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import { useTranslation } from 'react-i18next';

const AccessArea = () => {
  const { t } = useTranslation();
  const [filter, setFilter] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);

  const filteredData = AccessAreaData
    .filter((row) => (filter === "All" ? true : row.status === filter))
    .filter(
      (row) =>
        row.areaName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        row.pacsAreaName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        row.facility.toLowerCase().includes(searchTerm.toLowerCase()) ||
        row.system.toLowerCase().includes(searchTerm.toLowerCase())
    );

  const columns = [
    {
      name: <TruncatedCell text={t('access_area.area_name')} />,
      selector: (row) => row.areaName,
      sortable: true,
      cell: (row , index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() => window.open(`/access-details`, "_blank")}
        >
          <span className=" ">{row.areaName}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt="nd" />
          )}
        </div>
      ),
    },
    {
      name: <TruncatedCell text={t('access_area.pacs_area_name')} />,
      selector: (row) => row.pacsAreaName,
    },
    {
      name: t('access_area.facility'),
      selector: (row) => row.facility,
    },
    {
      name: t('access_area.system'),
      selector: (row) => row.system,
    },
    {
      name: t('access_area.online'),
      selector: (row) => row.online,
    },
    {
      name: <TruncatedCell text={t('access_area.requestable_in_self_service')} />,
      selector: (row) => row.requestable,
    },
    {
      name: <TruncatedCell text={t('access_area.area_types')} />,
      selector: (row) => row.areaTypes,
    },
    {
      name: <TruncatedCell text={t('access_area.card_types')} />,
      selector: (row) => row.cardTypes,
    },
    {
      name: t('access_area.status'),
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full`}
        >
          {t(`access_area.status_${row.status.toLowerCase()}`)}
        </span>
      ),
    },
  ];

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">{t('access_area.title')}</h2>
      </div>
      <div className="mb-4 flex items-center space-x-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: t('access_area.filter_all'), value: "All" },
            { label: t('access_area.filter_active'), value: "Active" },
            { label: t('access_area.filter_deleted'), value: "Deleted" },
          ]}
        />
      </div>
      <GenericTable
        title={t('access_area.title')}
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        showAddButton={false}
        extraControls={
          <IoFilter
            className="bg-white shadow-sm border p-1 text-[#4F2683] h-8 w-8 rounded cursor-pointer"
            onClick={() => setIsFilterPanelOpen(true)}
          />
        }
        data={filteredData}
        fixedHeader
        fixedHeaderScrollHeight="440px"
      />
      <FilterPanel isOpen={isFilterPanelOpen} onClose={() => setIsFilterPanelOpen(false)} />
    </div>
  );
};

export default AccessArea;
